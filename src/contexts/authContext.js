import { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Initialize user state from cookie on app load
  useEffect(() => {
    const initializeAuth = () => {
      const userId = Cookies.get('userId');
      const userData = Cookies.get('userData');
      
      if (userId && userData) {
        try {
          const parsedUserData = JSON.parse(userData);
          setUser(parsedUserData);
        } catch (error) {
          console.error('Error parsing user data:', error);
          // Clear invalid cookies
          Cookies.remove('userId');
          Cookies.remove('userData');
        }
      }
      setLoading(false);
    };

    initializeAuth();
  }, []);

  const login = (userData) => {
    setUser(userData);
    
    // Store user data in cookies
    Cookies.set('userId', userData._id, {
      expires: 1, // 1 day
      path: '/',
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });
    
    Cookies.set('userData', JSON.stringify(userData), {
      expires: 1, // 1 day
      path: '/',
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });
  };

  const logout = () => {
    setUser(null);
    Cookies.remove('userId');
    Cookies.remove('userData');
    router.push('/login');
  };

  const isAuthenticated = () => {
    return !!user;
  };

  const hasRole = (roles) => {
    if (!user) return false;
    if (Array.isArray(roles)) {
      return roles.includes(user.role);
    }
    return user.role === roles;
  };

  const isSuperAdmin = () => {
    return user?.role === 'superadmin';
  };

  const isComplianceOfficer = () => {
    return user?.role === 'complianceOfficer';
  };

  const value = {
    user,
    loading,
    login,
    logout,
    isAuthenticated,
    hasRole,
    isSuperAdmin,
    isComplianceOfficer
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};