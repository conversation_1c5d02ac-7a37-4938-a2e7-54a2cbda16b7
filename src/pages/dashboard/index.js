import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/authContext';
import Link from 'next/link';
import axios from 'axios';
import { parseISO, differenceInDays } from 'date-fns';
import config from "../../../config.json";

const StatCard = ({ title, value, icon, isLoading, href }) => (
    <div className="bg-white p-5 rounded-lg shadow-md flex items-center space-x-4">
        {icon && (
            <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                {icon}
            </div>
        )}
        <div>
            <h4 className="text-gray-500 text-sm font-medium">{title}</h4>
            {isLoading ? (
                <div className="mt-1 h-8 w-24 bg-gray-200 rounded animate-pulse"></div>
            ) : (
                <Link href={href || '#'} legacyBehavior>
                    <a className="text-2xl font-bold text-blue-600 mt-1 hover:text-blue-800 cursor-pointer">
                        {value}
                    </a>
                </Link>
            )}
        </div>
    </div>
);

const LenderCard = ({ name, logoUrl, isLoading }) => (
    <div className="bg-white p-4 rounded-lg shadow-md flex items-center space-x-3">
        {isLoading ? (
            <div className="h-10 w-10 rounded-full bg-gray-200 animate-pulse"></div>
        ) : (
            logoUrl && <img src={logoUrl} alt={`${name} Logo`} className="h-10 w-10 rounded-full object-contain" />
        )}
        <div>
            {isLoading ? (
                <div className="mt-1 h-6 w-32 bg-gray-200 rounded animate-pulse"></div>
            ) : (
                <h4 className="text-gray-800 font-medium">{name}</h4>
            )}
        </div>
    </div>
);

const AccordionSection = ({ title, actionHref, actionText, children }) => {
    const [isOpen, setIsOpen] = useState(true);

    return (
        <div className="bg-white rounded-xl shadow-lg mb-6">
            <div
                className="flex justify-between items-center p-5 cursor-pointer"
                onClick={() => setIsOpen(!isOpen)}
            >
                <h2 className="text-xl font-bold text-gray-800">{title}</h2>
                <div className="flex items-center space-x-4">
                    {actionHref && (
                        <Link href={actionHref} legacyBehavior>
                            <a
                                onClick={(e) => e.stopPropagation()}
                                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200 ease-in-out shadow-sm text-sm font-medium"
                            >
                                {actionText}
                            </a>
                        </Link>
                    )}
                    <svg
                        className={`w-6 h-6 text-gray-600 transform transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </div>
            </div>
            {isOpen && (
                <div className="p-5 border-t border-gray-200">
                    {children}
                </div>
            )}
        </div>
    );
};

export default function DashboardPage() {
    const { user } = useAuth();
    const [kycCount, setKycCount] = useState(0);
    const [dashboardData, setDashboardData] = useState({
        msme: { total: 0, pendingKyc: 0 },
        lender: { total: 0, list: [] },
        buyer: { total: 0, onboarded: 0, nonOnboarded: 0 },
        credit: { approved: 0, utilised: 0, underReview: 0, active: 0, suspended: 0 },
        invoice: { discounted: 0, pendingVerification: 0, activeDiscounted: 0 },
        repayment: { paid: 0, dueNext30: 0, late7to30: 0, late31to90: 0, overdue90plus: 0 },
    });
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    const getNested = (obj, path, defaultValue = undefined) => {
        const properties = path.split('.');
        return properties.reduce((acc, key) => (acc && acc[key] !== undefined && acc[key] !== null ? acc[key] : defaultValue), obj);
    };

    useEffect(() => {
        const fetchAdminSummaryData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const [
                    msmeRes,
                    creditRes,
                    invoiceRes,
                    repaymentRes,
                    lenderRes,
                    buyerRes
                ] = await Promise.allSettled([
                    axios.get(`${config.apiUrl}/ops/invoiceFinancing/getSubmittedKycs`),
                    axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLines`),
                    axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchInvoices`),
                    axios.get(`${config.apiUrl}/ops/invoiceFinancing/admin/all-lender-repayment-disbursal`),
                    axios.get(`${config.apiUrl}/ops/invoiceFinancing/lender-admins`),
                    axios.get(`${config.apiUrl}/ops/invoiceFinancing/buyers/all-with-msme`)
                ]);

                let msmeStats = { total: 0, pendingKyc: 0 };
                if (msmeRes.status === 'fulfilled' && msmeRes.value.data?.success) {
                    const kycs = msmeRes.value.data.kycs || [];
                    msmeStats.total = kycs.length;
                    msmeStats.pendingKyc = kycs.filter(k =>
                        k.kyc && ['INITIATED', 'UNDER_REVIEW'].includes(k.kyc.verificationStatus)
                    ).length;
                }

                let lenderStats = { total: 0, list: [] };
                if (lenderRes.status === 'fulfilled' && lenderRes.value.data?.success) {
                    lenderStats.total = lenderRes.value.data.data.length;
                    lenderStats.list = lenderRes.value.data.data.map(l => ({
                        _id: l._id,
                        lenderName: l.lenderName,
                        logoUrl: l.logoUrl
                    }));
                }

                let buyerStats = { total: 0, onboarded: 0, nonOnboarded: 0 };
                if (buyerRes.status === 'fulfilled' && Array.isArray(buyerRes.value.data)) {
                    const buyers = buyerRes.value.data;
                    buyerStats.total = buyers.length;
                    buyerStats.onboarded = buyers.filter(b => b.onboardingStatus === 'Onboarded').length;
                    buyerStats.nonOnboarded = buyers.filter(b => b.onboardingStatus === 'Non-Onboarded').length;
                }

                let creditStats = { approved: 0, utilised: 0, underReview: 0, active: 0, suspended: 0 };
                if (creditRes.status === 'fulfilled') {
                    const creditLines = creditRes.value.data || [];
                    creditStats.approved = creditLines
                        .filter(cl => cl.creditLineStatus === 'ACTIVE')
                        .reduce((sum, cl) => sum + (cl.creditLimit || 0), 0);
                    creditStats.utilised = creditLines
                        .reduce((sum, cl) => sum + (cl.utilizedAmount || 0), 0);
                    creditStats.underReview = creditLines.filter(cl => cl.creditLineStatus === 'UNDER_REVIEW').length;
                    creditStats.active = creditLines.filter(cl => cl.creditLineStatus === 'ACTIVE').length;
                    creditStats.suspended = creditLines.filter(cl => cl.creditLineStatus === 'SUSPENDED').length;
                }

                let invoiceStats = { discounted: 0, pendingVerification: 0, activeDiscounted: 0 };
                if (invoiceRes.status === 'fulfilled' && Array.isArray(invoiceRes.value.data)) { // <--- Added Array.isArray check here
                    const invoices = (invoiceRes.value.data || []).map(inv => inv._doc || inv);
                    invoiceStats.discounted = invoices.filter(inv => ['DISBURSED', 'LOAN_IN_PROGRESS', 'DEFAULT', 'WRITTEN_OFF_PAID'].includes(inv.status)).length;
                    invoiceStats.pendingVerification = invoices.filter(inv => ['VERIFICATION_PENDING_ANCHOR', 'VERIFICATION_PENDING_LENDER', 'MORE_INFO_NEEDED_ANCHOR', 'MORE_INFO_NEEDED_LENDER'].includes(inv.status)).length;
                    invoiceStats.activeDiscounted = invoices.filter(inv => inv.status === 'LOAN_IN_PROGRESS').length;
                }

                let repaymentStats = { paid: 0, dueNext30: 0, late7to30: 0, late31to90: 0, overdue90plus: 0 };
                if (repaymentRes.status === 'fulfilled' && repaymentRes.value.data?.success) {
                    const allLenderData = repaymentRes.value.data.data || [];
                    const allEmis = allLenderData.flatMap(lender => getNested(lender, 'data', []))
                        .flatMap(offer => getNested(offer, 'offerInfo.emiDetails', []));

                    const now = new Date();
                    allEmis.forEach(emi => {
                        if (emi.rePaymentStatus === 'PAID') {
                            repaymentStats.paid++;
                        } else if (emi.rePaymentStatus === 'PENDING') {
                            try {
                                const dueDate = parseISO(emi.rePaymentDate);
                                const daysDiff = differenceInDays(now, dueDate);

                                if (daysDiff < 0) {
                                    if (Math.abs(daysDiff) <= 30) {
                                        repaymentStats.dueNext30++;
                                    }
                                } else {
                                    if (daysDiff >= 7 && daysDiff <= 30) {
                                        repaymentStats.late7to30++;
                                    } else if (daysDiff >= 31 && daysDiff <= 90) {
                                        repaymentStats.late31to90++;
                                    } else if (daysDiff > 90) {
                                        repaymentStats.overdue90plus++;
                                    }
                                }
                            } catch (e) {
                                console.error("Could not parse EMI date:", e);
                            }
                        }
                    });
                }

                setDashboardData({
                    msme: msmeStats,
                    lender: lenderStats,
                    buyer: buyerStats,
                    credit: creditStats,
                    invoice: invoiceStats,
                    repayment: repaymentStats,
                });

            } catch (err) {
                console.error('Error fetching admin summary data:', err);
                setError(err.message || "An unknown error occurred.");
            } finally {
                setIsLoading(false);
            }
        };

        const fetchComplianceData = async () => {
            try {
                const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/getSubmittedKycs`);
                if (response.ok) {
                    const data = await response.json();
                    setKycCount(data.kycs.filter(k => k.kyc && ['INITIATED', 'UNDER_REVIEW'].includes(k.kyc.verificationStatus)).length);
                } else {
                    console.error('Failed to fetch KYC count for compliance');
                }
            } catch (error) {
                console.error('Error fetching KYC count for compliance:', error);
            }
        }

        if (user) {
            if (['superadmin', 'buyerAdmin', 'lenderAdmin'].includes(user.role)) {
                fetchAdminSummaryData();
            }
            if (user.role === 'complianceOfficer') {
                fetchComplianceData();
            }
        }
    }, [user]);

    const formatCurrency = (amount) => {
        const num = Number(amount);
        if (isNaN(num)) return 'QAR 0.00';
        return `QAR ${num.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    };

    if (!user) {
        return <div className="flex justify-center items-center h-screen text-gray-600">Loading user data...</div>;
    }

    if (user.role === 'complianceOfficer') {
        const complianceStats = [
            { name: 'Pending KYC Approvals', value: kycCount > 0 ? kycCount : '0', icon: 'user-check', href: '/kycApprovals' },
            { name: 'Total Compliance Reviews', value: '47', icon: 'clipboard-list', href: '/complianceReviews' },
            { name: 'Open Audit Items', value: '12', icon: 'exclamation-triangle', href: '/auditItems' },
            { name: 'Policy Updates Needed', value: '3', icon: 'file-alt', href: '/policyUpdates' },
        ];

        const recentActivities = [
            { date: '2025-06-22', activity: 'KYC Review Completed for ABC Trading LLC', status: 'Approved', type: 'KYC' },
            { date: '2025-06-21', activity: 'Risk Assessment Updated for XYZ Corp', status: 'Under Review', type: 'Risk' },
            { date: '2025-06-21', activity: 'AML Check Flagged for DEF Industries', status: 'Flagged', type: 'AML' },
            { date: '2025-06-20', activity: 'Compliance Report Generated Q2 2025', status: 'Completed', type: 'Report' },
            { date: '2025-06-20', activity: 'Document Verification for GHI Enterprises', status: 'Pending', type: 'Document' },
        ];

        const pendingTasks = [
            { task: 'Review KYC documentation for new MSME applicants', priority: 'High', dueDate: '2025-06-25' },
            { task: 'Update AML policy guidelines', priority: 'Medium', dueDate: '2025-06-30' },
            { task: 'Conduct quarterly compliance audit', priority: 'High', dueDate: '2025-06-28' },
            { task: 'Review lender risk assessment criteria', priority: 'Low', dueDate: '2025-07-05' },
        ];

        return (
            <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-3xl font-extrabold text-gray-800">Compliance Officer Dashboard</h1>
                    <div className="flex space-x-3">
                        <select className="border border-gray-300 rounded-lg p-2 text-base shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option>Last 7 Days</option>
                            <option>Last 30 Days</option>
                            <option>Last 90 Days</option>
                            <option>Year to Date</option>
                        </select>
                        <button className="bg-blue-600 text-white px-5 py-2 rounded-lg hover:bg-blue-700 transition duration-200 ease-in-out shadow-md">
                            Generate Report
                        </button>
                    </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    {complianceStats.map((stat) => (
                        <div key={stat.name} className="bg-white p-6 rounded-xl shadow-lg flex items-center space-x-4">
                            <div className="h-14 w-14 rounded-full bg-blue-100 flex items-center justify-center">
                                <svg className="h-7 w-7 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    {stat.icon === 'user-check' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 3a9 9 0 11-18 0 9 9 0 0118 0z" />}
                                    {stat.icon === 'clipboard-list' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />}
                                    {stat.icon === 'exclamation-triangle' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.332 16c-.77 1.333.192 3 1.732 3z" />}
                                    {stat.icon === 'file-alt' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />}
                                </svg>
                            </div>
                            <div>
                                <h3 className="text-gray-600 text-sm">{stat.name}</h3>
                                <Link href={stat.href} legacyBehavior>
                                    <a className="text-3xl font-bold text-blue-600 mt-1 hover:text-blue-800 cursor-pointer">
                                        {stat.value}
                                    </a>
                                </Link>
                            </div>
                        </div>
                    ))}
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-white rounded-xl shadow-lg p-6">
                        <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Activities</h3>
                        <div className="space-y-3">
                            {recentActivities.map((activity, index) => (
                                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div className="flex-1">
                                        <p className="text-sm font-medium text-gray-800">{activity.activity}</p>
                                        <p className="text-xs text-gray-500 mt-1">{activity.date} • {activity.type}</p>
                                    </div>
                                    <span className={`px-2 py-1 text-xs rounded-full ${activity.status === 'Approved' ? 'bg-green-100 text-green-800' :
                                        activity.status === 'Flagged' ? 'bg-red-100 text-red-800' :
                                            activity.status === 'Completed' ? 'bg-blue-100 text-blue-800' :
                                                'bg-yellow-100 text-yellow-800'
                                        }`}>
                                        {activity.status}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-lg p-6">
                        <h3 className="text-lg font-semibold text-gray-800 mb-4">Pending Tasks</h3>
                        <div className="space-y-3">
                            {pendingTasks.map((task, index) => (
                                <div key={index} className="p-3 bg-gray-50 rounded-lg">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <p className="text-sm font-medium text-gray-800">{task.task}</p>
                                            <p className="text-xs text-gray-500 mt-1">Due: {task.dueDate}</p>
                                        </div>
                                        <span className={`px-2 py-1 text-xs rounded-full ${task.priority === 'High' ? 'bg-red-100 text-red-800' :
                                            task.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                                'bg-gray-100 text-gray-800'
                                            }`}>
                                            {task.priority}
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Compliance Metrics</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <p className="text-2xl font-bold text-green-600">98.5%</p>
                            <p className="text-sm text-gray-600">KYC Approval Rate</p>
                        </div>
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <p className="text-2xl font-bold text-blue-600">2.3 days</p>
                            <p className="text-sm text-gray-600">Avg Review Time</p>
                        </div>
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <p className="text-2xl font-bold text-purple-600">156</p>
                            <p className="text-sm text-gray-600">Reviews This Month</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-extrabold text-gray-800">Admin Summary</h1>
                <button
                    onClick={() => window.location.reload()}
                    className="bg-white text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-100 transition duration-200 ease-in-out shadow-sm border border-gray-300 text-sm font-medium"
                    disabled={isLoading}
                >
                    {isLoading ? 'Loading...' : 'Refresh Data'}
                </button>
            </div>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            <AccordionSection title="MSMEs" actionHref="/kycApprovals" actionText="Manage MSMEs">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <StatCard title="Total MSMEs Onboarded" value={dashboardData.msme.total} isLoading={isLoading} href="/kycApprovals" />
                    <StatCard title="MSMEs Awaiting KYC Approval" value={dashboardData.msme.pendingKyc} isLoading={isLoading} href="/kycApprovals" />
                </div>
            </AccordionSection>

            <AccordionSection title="Buyers" actionHref="/buyerReport" actionText="Buyers">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <StatCard title="Total Buyers" value={dashboardData.buyer.total} isLoading={isLoading} href="/buyerReport" />
                    <StatCard title="Onboarded Buyers" value={dashboardData.buyer.onboarded} isLoading={isLoading} href="/buyerReport" />
                    <StatCard title="Non-Onboarded Buyers" value={dashboardData.buyer.nonOnboarded} isLoading={isLoading} href="/buyerReport" />
                </div>
            </AccordionSection>

            <AccordionSection title="Lenders">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <StatCard title="Total Lenders Onboarded" value={dashboardData.lender.total} isLoading={isLoading} />
                </div>
                {!isLoading && dashboardData.lender.list.length > 0 && (
                    <div className="mt-4">
                        <h3 className="text-lg font-semibold text-gray-700 mb-3">Individual Lenders:</h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            {dashboardData.lender.list.map(lender => (
                                <LenderCard key={lender._id} name={lender.lenderName} logoUrl={lender.logoUrl} isLoading={isLoading} />
                            ))}
                        </div>
                    </div>
                )}
                {isLoading && (
                    <div className="mt-4">
                        <h3 className="text-lg font-semibold text-gray-700 mb-3">Individual Lenders:</h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            {Array.from({ length: 3 }).map((_, i) => (
                                <LenderCard key={i} isLoading={true} />
                            ))}
                        </div>
                    </div>
                )}
                {!isLoading && dashboardData.lender.list.length === 0 && (
                    <p className="text-gray-500">No lenders onboarded yet.</p>
                )}
            </AccordionSection>

            <AccordionSection title="Credit" actionHref="/creditAssessment" actionText="Manage Credit Lines">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <StatCard title="Total Credit Line Approved" value={formatCurrency(dashboardData.credit.approved)} isLoading={isLoading} href="/creditAssessment" />
                    <StatCard title="Total Credit Line Utilised" value={formatCurrency(dashboardData.credit.utilised)} isLoading={isLoading} href="/creditAssessment" />
                    <StatCard title="Credit Lines Under Review" value={dashboardData.credit.underReview} isLoading={isLoading} href="/creditAssessment" />
                    <StatCard title="Active Credit Lines" value={dashboardData.credit.active} isLoading={isLoading} href="/creditAssessment" />
                    <StatCard title="Suspended Credit Lines" value={dashboardData.credit.suspended} isLoading={isLoading} href="/creditAssessment" />
                </div>
            </AccordionSection>

            <AccordionSection title="Invoices" actionHref="/invoices" actionText="Manage Invoices">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <StatCard title="Total Invoices Discounted" value={dashboardData.invoice.discounted} isLoading={isLoading} href="/invoices" />
                    <StatCard title="Invoices Awaiting Verification" value={dashboardData.invoice.pendingVerification} isLoading={isLoading} href="/invoices" />
                    <StatCard title="Active Discounted Invoices" value={dashboardData.invoice.activeDiscounted} isLoading={isLoading} href="/invoices" />
                </div>
            </AccordionSection>

            <AccordionSection title="Repayments" actionHref="/payments" actionText="Manage Repayments">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                    <StatCard title="Total EMIs Paid" value={dashboardData.repayment.paid} isLoading={isLoading} href="/payments" />
                    <StatCard title="EMI Due in next 30 days" value={dashboardData.repayment.dueNext30} isLoading={isLoading} href="/payments" />
                    <StatCard title="EMIs Late (7-30 days)" value={dashboardData.repayment.late7to30} isLoading={isLoading} href="/payments" />
                    <StatCard title="EMIs Late (31-90 days)" value={dashboardData.repayment.late31to90} isLoading={isLoading} href="/payments" />
                    <StatCard title="EMIs Overdue (>90 days)" value={dashboardData.repayment.overdue90plus} isLoading={isLoading} href="/payments" />
                </div>
            </AccordionSection>
        </div>
    );
}