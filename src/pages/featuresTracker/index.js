import { useState, useEffect, useMemo, useCallback } from 'react';
import StatusBadge from '../../components/StatusBadge';
import { useAuth } from '../../contexts/authContext';
import * as XLSX from 'xlsx';
import Link from 'next/link';

export default function FeaturesDefectsTrackerPage() {
    const { user } = useAuth();
    const [featuresDefectTrackerData, setFeaturesDefectTrackerData] = useState([]);
    const [mvpWipPlanData, setMvpWipPlanData] = useState([]);
    const [excelDataLoading, setExcelDataLoading] = useState(false);
    const [excelDataError, setExcelDataError] = useState(null);

    const [featuresFilters, setFeaturesFilters] = useState({});
    // const [mvpFilters, setMvpFilters] = useState({});

    // These URLs should point to your static CSV files in the public folder
    const FEATURES_DEFECT_TRACKER_URL = '/1.csv'; // Make sure this path is correct
    const MVP_WIP_PLAN_URL = '/2.csv'; // Make sure this path is correct

    useEffect(() => {
        async function fetchExcelData() {
            setExcelDataLoading(true);
            setExcelDataError(null);
            try {
                // --- Fetch and parse Features-Defect_Tracker data (1.csv) ---
                const featuresDefectsResponse = await fetch(FEATURES_DEFECT_TRACKER_URL);
                if (!featuresDefectsResponse.ok) {
                    throw new Error(`Failed to load ${FEATURES_DEFECT_TRACKER_URL}: ${featuresDefectsResponse.statusText}`);
                }
                const featuresDefectsCsvText = await featuresDefectsResponse.text();
                const featuresDefectsWorkbook = XLSX.read(featuresDefectsCsvText, { type: 'string' });
                const featuresDefectsSheet = featuresDefectsWorkbook.Sheets[featuresDefectsWorkbook.SheetNames[0]];

                const rawFeaturesDefectsData = XLSX.utils.sheet_to_json(featuresDefectsSheet, { header: 1, raw: false });

                if (rawFeaturesDefectsData && rawFeaturesDefectsData.length > 1) {
                    // Assuming the first header row is at index 1 and data starts from index 2
                    const headers = rawFeaturesDefectsData[1];
                    const dataRows = rawFeaturesDefectsData.slice(2);

                    const formattedFeaturesDefects = dataRows.map(row => {
                        let obj = {};
                        headers.forEach((header, index) => {
                            const cleanedHeader = header
                                ? String(header).replace(/[^a-zA-Z0-9\s\/]/g, '').trim()
                                    .replace(/\s+/g, ' ')
                                    .replace(/ /g, '_')
                                : `Unnamed_${index}`;
                            obj[cleanedHeader] = row[index];
                        });
                        obj.uniqueId = obj.Issue_ID || `features-${Date.now()}-${Math.random()}`;
                        return obj;
                    }).filter(item => item['Issue_ID'] !== undefined && item['Issue_ID'] !== null && item['Issue_ID'] !== '');

                    setFeaturesDefectTrackerData(formattedFeaturesDefects);
                } else {
                    setFeaturesDefectTrackerData([]);
                }

                // --- Fetch and parse MVP_WIP_Plan data (2.csv) ---
                const mvpWipPlanResponse = await fetch(MVP_WIP_PLAN_URL);
                if (!mvpWipPlanResponse.ok) {
                    throw new Error(`Failed to load ${MVP_WIP_PLAN_URL}: ${mvpWipPlanResponse.statusText}`);
                }
                const mvpWipPlanCsvText = await mvpWipPlanResponse.text();
                const mvpWipPlanWorkbook = XLSX.read(mvpWipPlanCsvText, { type: 'string' });
                const mvpWipPlanSheet = mvpWipPlanWorkbook.Sheets[mvpWipPlanWorkbook.SheetNames[0]];

                const rawMvpWipPlanData = XLSX.utils.sheet_to_json(mvpWipPlanSheet, { raw: false });
                const formattedMvpWipPlan = rawMvpWipPlanData.map((row, idx) => ({
                    Bucket: row.Bucket,
                    Requirement: row.Requirement,
                    Details: row.Details,
                    Tech: row.Tech,
                    Status: row.Status,
                    Owner: row.Owner,
                    'Start Date': row['Start Date'],
                    'End Date': row['End Date'],
                    uniqueId: `${row.Bucket}-${row.Requirement}-${idx}`.replace(/[^a-zA-Z0-9-]/g, '') || `mvp-${Date.now()}-${idx}-${Math.random()}`,
                })).filter(item => item.Bucket !== undefined && item.Bucket !== null && item.Bucket !== '');

                setMvpWipPlanData(formattedMvpWipPlan);

            } catch (error) {
                console.error('Error fetching or parsing Excel data:', error);
                setExcelDataError(`Failed to load compliance data: ${error.message}. Please check CSV files and their location in the public folder. Ensure correct headers.`);
                setFeaturesDefectTrackerData([]);
                setMvpWipPlanData([]);
            } finally {
                setExcelDataLoading(false);
            }
        }

        if (user) {
            fetchExcelData();
        }
    }, [user]);

    const handleFeaturesFilterChange = useCallback((columnName, value) => {
        setFeaturesFilters(prevFilters => ({
            ...prevFilters,
            [columnName]: value.toLowerCase()
        }));
    }, []);

    const resetFeaturesFilters = useCallback(() => {
        setFeaturesFilters({});
    }, []);

    const filteredFeaturesDefectTrackerData = useMemo(() => {
        return featuresDefectTrackerData.filter(item => {
            return Object.entries(featuresFilters).every(([columnName, filterValue]) => {
                if (!filterValue) return true;
                const cellValue = String(item[columnName]).toLowerCase();
                return cellValue.includes(filterValue);
            });
        });
    }, [featuresDefectTrackerData, featuresFilters]);

    // const handleMvpFilterChange = useCallback((columnName, value) => {
    //     setMvpFilters(prevFilters => ({
    //         ...prevFilters,
    //         [columnName]: value.toLowerCase()
    //     }));
    // }, []);

    // const resetMvpFilters = useCallback(() => {
    //     setMvpFilters({});
    // }, []);

    // const filteredMvpWipPlanData = useMemo(() => {
    //     return mvpWipPlanData.filter(item => {
    //         return Object.entries(mvpFilters).every(([columnName, filterValue]) => {
    //             if (!filterValue) return true;
    //             const cellValue = String(item[columnName]).toLowerCase();
    //             return cellValue.includes(filterValue);
    //         });
    //     });
    // }, [mvpWipPlanData, mvpFilters]);

    if (!user) {
        return <div className="flex justify-center items-center h-screen text-gray-600">Loading user data...</div>;
    }

    return (
        <div className="p-6 space-y-8 bg-gray-50 min-h-screen">
            <Link href="/reports">
                <button className="mb-4 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50">
                    ← Back to Reports
                </button>
            </Link>
            <h1 className="text-3xl font-extrabold text-gray-800 mb-6 border-b pb-4">Features and Defects tracker</h1>

            {excelDataLoading && (
                <div className="flex items-center justify-center py-10">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
                    <p className="ml-4 text-blue-600 text-lg">Loading compliance data from static Excel sheets...</p>
                </div>
            )}

            {excelDataError && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg shadow-md" role="alert">
                    <strong className="font-bold">Error! </strong>
                    <span className="block sm:inline">{excelDataError}</span>
                </div>
            )}

            {/* Features-Defect Tracker Table */}
            {!excelDataLoading && (featuresDefectTrackerData.length > 0 || Object.keys(featuresFilters).some(k => featuresFilters[k])) && (
                <div className="bg-white rounded-xl shadow-lg p-7">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-2xl font-bold text-gray-700">Defects tracker table</h2>
                        <button
                            onClick={resetFeaturesFilters}
                            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                        >
                            Reset Filters
                        </button>
                    </div>
                    <div className="overflow-x-auto rounded-lg border border-gray-200">
                        <table className="min-w-full divide-y divide-gray-200 table-auto">
                            <thead className="bg-gray-100">
                                <tr>
                                    {featuresDefectTrackerData.length > 0 && Object.keys(featuresDefectTrackerData[0]).filter(key => key !== 'uniqueId').map(key => (
                                        <th
                                            key={key}
                                            className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider sticky top-0 bg-gray-100 z-10"
                                        >
                                            <div className="flex flex-col">
                                                <span className="mb-1">{key.replace(/_/g, ' ')}</span>
                                                <input
                                                    type="text"
                                                    placeholder={`Filter ${key.replace(/_/g, ' ')}`}
                                                    value={featuresFilters[key] || ''}
                                                    onChange={(e) => handleFeaturesFilterChange(key, e.target.value)}
                                                    className="p-1 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 w-full"
                                                />
                                            </div>
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200">
                                {filteredFeaturesDefectTrackerData.map((item) => (
                                    <tr key={item.uniqueId} className="hover:bg-gray-50">
                                        {Object.keys(item).filter(key => key !== 'uniqueId').map(key => {
                                            const cellValue = item[key];
                                            const commonClasses = "px-4 py-3 text-sm text-gray-800";

                                            if (key === 'Status') {
                                                return (
                                                    <td key={key} className={`${commonClasses} whitespace-nowrap`}>
                                                        <StatusBadge status={(cellValue || 'UNKNOWN').toUpperCase().replace(/ /g, '_')} />
                                                    </td>
                                                );
                                            }
                                            if (['Description', 'Steps_to_Reproduce/_Change', 'Resolution_Comments'].includes(key)) {
                                                return (
                                                    <td key={key} className={`${commonClasses} whitespace-normal max-w-xs`}>
                                                        {cellValue}
                                                    </td>
                                                );
                                            }
                                            return (
                                                <td key={key} className={`${commonClasses} whitespace-normal`}>
                                                    {cellValue}
                                                </td>
                                            );
                                        })}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    {filteredFeaturesDefectTrackerData.length === 0 && (
                        <div className="text-center py-8 text-gray-500">No matching data found for the applied filters. Please try resetting the filters.</div>
                    )}
                </div>
            )}

            {/* Message if no data is loaded at all (initial state or error) */}
            {!excelDataLoading && !excelDataError && featuresDefectTrackerData.length === 0 && mvpWipPlanData.length === 0 && (
                <div className="text-center text-gray-500 py-10">
                    No compliance data loaded. Please ensure the CSV files `1.csv` and `2.csv` are in the `public` folder and have the correct structure.
                </div>
            )}
        </div>
    );
}