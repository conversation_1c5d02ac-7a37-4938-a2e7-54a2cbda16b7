// src/pages/live-cases/index.js
import { useState } from 'react';
import StatusBadge from '../../components/StatusBadge';

export default function LiveCases() {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const cases = {
    active: [
      {
        id: 1,
        company: "Tech Solutions Ltd",
        invoiceAmount: "QAR 10,00,000",
        discountedAmount: "QAR 9,50,000",
        dueDate: "2024-03-15",
        status: "Active",
        paymentStatus: "On Time",
        disbursementDate: "2024-02-15",
        tenure: "30 days"
      },
      {
        id: 2,
        company: "Manufacturing Pro",
        invoiceAmount: "QAR 15,00,000",
        discountedAmount: "QAR 14,25,000",
        dueDate: "2024-03-20",
        status: "Active",
        paymentStatus: "Due Soon",
        disbursementDate: "2024-02-20",
        tenure: "30 days"
      }
    ],
    closed: [
      {
        id: 3,
        company: "Global Trade Co",
        invoiceAmount: "QAR 5,00,000",
        discountedAmount: "QAR 4,75,000",
        dueDate: "2024-02-01",
        status: "Closed",
        paymentStatus: "Paid",
        disbursementDate: "2024-01-01",
        tenure: "30 days",
        closedDate: "2024-02-01"
      },
      {
        id: 4,
        company: "Supply Chain Inc",
        invoiceAmount: "QAR 8,00,000",
        discountedAmount: "QAR 7,60,000",
        dueDate: "2024-02-10",
        status: "Closed",
        paymentStatus: "Paid",
        disbursementDate: "2024-01-10",
        tenure: "30 days",
        closedDate: "2024-02-10"
      }
    ]
  };

  // Summary calculations
  const totalActive = cases.active.reduce((sum, item) =>
    sum + parseInt(item.discountedAmount.replace(/[^0-9]/g, '')), 0);
  const totalClosed = cases.closed.reduce((sum, item) =>
    sum + parseInt(item.discountedAmount.replace(/[^0-9]/g, '')), 0);

  // const getStatusStyle = (status) => {
  //   const styles = {
  //     'Active': 'bg-green-100 text-green-800',
  //     'Closed': 'bg-gray-100 text-gray-800',
  //     'On Time': 'bg-blue-100 text-blue-800',
  //     'Due Soon': 'bg-yellow-100 text-yellow-800',
  //     'Paid': 'bg-purple-100 text-purple-800'
  //   };
  //   return styles[status] || 'bg-gray-100 text-gray-800';
  // };

  const filteredCases = [...cases.active, ...cases.closed]
    .filter(item => {
      if (filter === 'active') return item.status === 'Active';
      if (filter === 'closed') return item.status === 'Closed';
      return true;
    })
    .filter(item =>
      item.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.invoiceAmount.toLowerCase().includes(searchTerm.toLowerCase())
    );

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Live Cases</h1>
        <div className="flex space-x-4">
          <input
            type="text"
            placeholder="Search cases..."
            className="border rounded-md p-2"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <select
            className="border rounded-md p-2"
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
          >
            <option value="all">All Cases</option>
            <option value="active">Active Only</option>
            <option value="closed">Closed Only</option>
          </select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-600 text-sm">Total Active Amount</h3>
          <p className="text-2xl font-bold">QAR {(totalActive / 100000).toFixed(2)} L</p>
          <p className="text-sm text-gray-600">{cases.active.length} active cases</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-600 text-sm">Total Closed Amount</h3>
          <p className="text-2xl font-bold">QAR {(totalClosed / 100000).toFixed(2)} L</p>
          <p className="text-sm text-gray-600">{cases.closed.length} closed cases</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-600 text-sm">Total Portfolio</h3>
          <p className="text-2xl font-bold">QAR {((totalActive + totalClosed) / 100000).toFixed(2)} L</p>
          <p className="text-sm text-gray-600">{cases.active.length + cases.closed.length} total cases</p>
        </div>
      </div>

      {/* Cases Table */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="p-4 text-left">Company</th>
              <th className="p-4 text-left">Invoice Amount</th>
              <th className="p-4 text-left">Discounted Amount</th>
              <th className="p-4 text-left">Disbursement Date</th>
              <th className="p-4 text-left">Due Date</th>
              <th className="p-4 text-left">Tenure</th>
              <th className="p-4 text-left">Status</th>
              <th className="p-4 text-left">Payment Status</th>
              <th className="p-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredCases.map((item) => (
              <tr key={item.id} className="border-t hover:bg-gray-50">
                <td className="p-4 font-medium">{item.company}</td>
                <td className="p-4">{item.invoiceAmount}</td>
                <td className="p-4">{item.discountedAmount}</td>
                <td className="p-4">{item.disbursementDate}</td>
                <td className="p-4">{item.dueDate}</td>
                <td className="p-4">{item.tenure}</td>
                <td className="p-4">
                  <StatusBadge status={item.status.toUpperCase()} />
                </td>
                <td className="p-4">
                  <StatusBadge status={item.paymentStatus.toUpperCase().replace(' ', '_')} />
                </td>
                <td className="p-4">
                  <div className="flex space-x-2">
                    <button className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600">
                      View Details
                    </button>
                    {item.status === 'Active' && (
                      <button className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600">
                        Mark as Closed
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

