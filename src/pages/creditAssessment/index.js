import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios'; // Using axios for offer and update API calls
import { Disclosure } from '@headlessui/react';
import { ChevronUpIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useMemo } from 'react'; // Add useMemo if not already imported
import StatusBadge from '../../components/StatusBadge';
import { formatPhoneNumber } from '../../utils/phoneFormatter';
import config from "../../../config.json"
import * as XLSX from 'xlsx';

// --- Constants for Processing Fee ---
const MAX_PROCESSING_FEE_FLAT = 500;
const MAX_PROCESSING_FEE_PERCENTAGE = 5; // 5%

const CREDIT_LINE_STATUS_ORDER = {
    'UNDER_REVIEW': 1,
    'DRAFT': 2,
    'ON_HOLD': 3,
    'APPROVED': 4,
    'ACTIVE': 5,
    'SUSPENDED': 6,
    'EXPIRED': 7,
    'REJECTED': 8
  };
  const DEFAULT_CREDIT_LINE_STATUS_PRIORITY = Math.max(...Object.values(CREDIT_LINE_STATUS_ORDER), 0) + 1;

  
// --- Helper Function: Get Nested ---

// --- AssessmentFilterSection Component (Similar to the previous one) ---
// --- Enhanced AssessmentFilterSection Component ---
const AssessmentFilterSection = ({ filters, setFilters, resetFilters }) => {
    // Input styling (same as before)
    const inputBaseClass = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
    const numberInputClass = `${inputBaseClass} px-2 py-1`;
    const dateInputClass = `${inputBaseClass} px-2 py-1`;
    const textInputClass = `${inputBaseClass} px-3 py-1.5`;
    const selectInputClass = `${inputBaseClass} px-3 py-2`; // Specific style for selects
    //uat comment
    const handleFilterChange = (event) => {
        const { name, value } = event.target;
        setFilters(prevFilters => ({
            ...prevFilters,
            [name]: value,
        }));
    };

    // Calculate active filters
    const activeFilterCount = useMemo(() => {
        return Object.values(filters).filter(value => value !== '' && value !== null && value !== undefined).length;
    }, [filters]);

    const handleReset = (event) => {
        event.stopPropagation();
        resetFilters();
    };

    // Define options based on your schema enums
    const offerStatusOptions = [
        'PENDING', 'ACCEPTED', 'LOAN_CONTRACT_ACCEPTED', 'INITIATED_FUND_TRANSFER',
        'READY_FOR_DISBURSAL', 'LOAN_IN_PROGRESS', 'PAID', 'LOAN_CANCELLED',
        'REJECTED', 'EXPIRED', 'WRITTEN_OFF', 'DEFAULTED', 'OVERDUE'
    ];
    const creditLineStatusOptions = [
        'DRAFT', 'ON_HOLD', 'UNDER_REVIEW', 'APPROVED', 'REJECTED',
        'ACTIVE', 'SUSPENDED', 'EXPIRED'
    ];
    // Note: KYC Status might come from User schema, define options accordingly
    const kycStatusOptions = [
        'APPROVED', 'VERIFIED', 'REJECTED', 'PENDING', 'UNDER_REVIEW',
        'INFO_NEEDED', 'REVIEW', 'SUBMITTED', 'INITIATED', 'REINITIATED', 'N/A'
    ];


    return (
        <div className="mb-6"> {/* Margin below the whole filter section */}
            <Disclosure as="div" className="border border-gray-200 rounded-lg shadow-sm bg-white">
                {({ open }) => (
                    <>
                        {/* The Filter Header Bar */}
                        <div className="flow-root">
                            <Disclosure.Button className="flex w-full items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring focus-visible:ring-indigo-500 focus-visible:ring-opacity-75">
                                <span className="flex items-center">
                                    <FunnelIcon className="mr-2 h-5 w-5 text-gray-400" aria-hidden="true" />
                                    Filters
                                    {activeFilterCount > 0 && (
                                        <span className="ml-2 rounded-full bg-gray-200 px-2 py-0.5 text-xs font-medium text-gray-800">
                                            {activeFilterCount}
                                        </span>
                                    )}
                                </span>
                                <span className="ml-6 flex items-center">
                                    <ChevronUpIcon
                                        className={`${open ? 'rotate-180 transform' : ''} h-5 w-5 text-gray-500 transition-transform duration-150 ease-in-out`}
                                    />
                                </span>
                            </Disclosure.Button>
                        </div>

                        {/* Separator */}
                        {open && <div className="border-t border-gray-200"></div>}

                        {/* Panel with Filter Inputs */}
                        <Disclosure.Panel className="px-4 py-5 sm:px-6 lg:px-8">
                            {/* Top row: Search & Clear */}
                            <div className="mb-6 flex items-start justify-between gap-4"> {/* Increased bottom margin */}
                                <div className="flex-1"> {/* Search takes most space */}
                                    <label htmlFor="assessSearchTerm" className="sr-only">Search (Name)</label>
                                    <input
                                        type="text" name="searchTerm" id="assessSearchTerm"
                                        value={filters.searchTerm} onChange={handleFilterChange}
                                        className={textInputClass} placeholder="Search Business or Borrower..."
                                    />
                                </div>
                                <button
                                    type="button" onClick={handleReset}
                                    className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                >
                                    <XMarkIcon className="-ml-1 mr-1.5 h-4 w-4 text-gray-400" aria-hidden="true" />
                                    Clear all
                                </button>
                            </div>

                            {/* Grid for other filters - Adjust grid columns as needed */}
                            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">

                                {/* Applied Date Range */}
                                <div className="sm:col-span-2 md:col-span-2 lg:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Applied Date Range</label>
                                    <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:items-center sm:space-x-2">
                                        <input type="date" name="startDate" value={filters.startDate} onChange={handleFilterChange} className={dateInputClass + ' flex-1'} aria-label="Start Date" />
                                        <span className="text-gray-500 text-center hidden sm:inline">to</span>
                                        <input type="date" name="endDate" value={filters.endDate} onChange={handleFilterChange} className={dateInputClass + ' flex-1'} aria-label="End Date" />
                                    </div>
                                </div>

                                {/* Offer Credit Limit Range */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Offer Limit (QAR)</label>
                                    <div className="flex space-x-2">
                                        <input type="number" name="minOfferLimit" value={filters.minOfferLimit} onChange={handleFilterChange} placeholder="Min" className={numberInputClass} />
                                        <input type="number" name="maxOfferLimit" value={filters.maxOfferLimit} onChange={handleFilterChange} placeholder="Max" className={numberInputClass} />
                                    </div>
                                </div>

                                {/* Offer Status Dropdown */}
                                <div>
                                    <label htmlFor="offerStatus" className="block text-sm font-medium text-gray-700 mb-1">Offer Status</label>
                                    <select id="offerStatus" name="offerStatus" value={filters.offerStatus} onChange={handleFilterChange} className={selectInputClass}>
                                        <option value="">All Statuses</option>
                                        {offerStatusOptions.map(status => <option key={status} value={status}>{status.replace('_', ' ')}</option>)}
                                    </select>
                                </div>

                                {/* Credit Line Status Dropdown */}
                                <div>
                                    <label htmlFor="creditLineStatus" className="block text-sm font-medium text-gray-700 mb-1">Credit Line Status</label>
                                    <select id="creditLineStatus" name="creditLineStatus" value={filters.creditLineStatus} onChange={handleFilterChange} className={selectInputClass}>
                                        <option value="">All Statuses</option>
                                        {creditLineStatusOptions.map(status => <option key={status} value={status}>{status.replace('_', ' ')}</option>)}
                                    </select>
                                </div>

                                {/* KYC Status Dropdown */}
                                <div>
                                    <label htmlFor="kycStatus" className="block text-sm font-medium text-gray-700 mb-1">KYC Status</label>
                                    <select id="kycStatus" name="kycStatus" value={filters.kycStatus} onChange={handleFilterChange} className={selectInputClass}>
                                        <option value="">All Statuses</option>
                                        {kycStatusOptions.map(status => <option key={status} value={status}>{status.replace('_', ' ')}</option>)}
                                    </select>
                                </div>

                                {/* Add more filters here if needed */}

                            </div>
                        </Disclosure.Panel>
                    </>
                )}
            </Disclosure>
            {/* REMOVED ActiveFilterTags component */}
        </div>
    );
};


// --- Helper Function: Calculate Age ---
// Returns a string like "X days ago", "Y hours ago", etc.
const calculateAge = (dateString) => {
    if (!dateString) return 'N/A';
    try {
        const startDate = new Date(dateString);
        if (isNaN(startDate.getTime())) return 'Invalid Date';

        const now = new Date();
        const diffMs = now - startDate;
        const diffSeconds = Math.floor(diffMs / 1000);
        const diffMinutes = Math.floor(diffSeconds / 60);
        const diffHours = Math.floor(diffMinutes / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffDays > 0) {
            return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        } else if (diffHours > 0) {
            return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        } else if (diffMinutes > 0) {
            return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
        } else {
            return `Just now`;
        }
    } catch (e) {
        console.error("Error calculating age:", dateString, e);
        return 'Error';
    }
};


const RenderInfoItemViewOnly = ({ label, value }) => {
    const displayValue = (val) => {
        if (val === null || val === undefined || val === '') {
            return <span className="text-gray-500 italic">N/A</span>;
        }
        if (typeof val === 'boolean') {
            return val ? 'Yes' : 'No';
        }
        // Handle phone number fields with proper formatting and clickable links
        if (['Mobile Number', 'Mobile', 'Contact Phone', 'Phone Number', 'Phone', 'Contact'].includes(label)) {
            return formatPhoneNumber(val);
        }
        // Add specific formatting if needed (e.g., for currency)
        return String(val);
    };

    //build check

    return (
        <div className="py-1 break-words text-sm">
            <span className="font-medium text-gray-600">{label}: </span>
            <span className="text-gray-800">{displayValue(value)}</span>
        </div>
    );
};

const RenderDocumentDisplayViewOnly = ({ documentData, label }) => {
    if (!documentData || !documentData.verificationStatus) {
        return (
            <div className="bg-white p-3 rounded-lg shadow border border-gray-200 h-full flex flex-col justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-800 mb-1">{label}</p>
                    <p className="text-xs text-gray-500 italic">Not Submitted</p>
                </div>
            </div>
        );
    }

    const status = String(documentData.verificationStatus || 'N/A').toUpperCase();
    const statusColorClasses = {
        VERIFIED: 'bg-green-100 text-green-800', APPROVED: 'bg-green-100 text-green-800',
        REJECTED: 'bg-red-100 text-red-800',
        PENDING: 'bg-yellow-100 text-yellow-800', REVIEW: 'bg-yellow-100 text-yellow-800', UNDER_REVIEW: 'bg-blue-100 text-blue-800', INFO_NEEDED: 'bg-yellow-100 text-yellow-800',
        SUBMITTED: 'bg-blue-100 text-blue-800',
        INITIATED: 'bg-gray-100 text-gray-600', REINITIATED: 'bg-gray-100 text-gray-600',
        'N/A': 'bg-gray-100 text-gray-600',
    };
    const statusClass = statusColorClasses[status] || statusColorClasses['N/A'];

    return (
        <div className="bg-white p-3 rounded-lg shadow border border-gray-200 h-full flex flex-col justify-between">
            <div>
                <div className="flex justify-between items-start mb-1">
                    <p className="text-sm font-medium text-gray-800 flex-1 mr-2">{label}</p>
                    <span className={`px-2 py-0.5 inline-flex text-xs leading-4 font-semibold rounded-full ${statusClass}`}>
                        {status.replace('_', ' ')}
                    </span>
                </div>
                {documentData.signedUrl && (
                    <a
                        href={documentData.signedUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-indigo-600 hover:text-indigo-800 text-xs break-all block underline mb-1"
                        title={documentData.filePath || 'View Document'}
                    >
                        View Document ({documentData.mimeType || 'PDF'})
                    </a>
                )}
                {!documentData.signedUrl && documentData.filePath && (
                    <p className="text-xs text-gray-500 italic mb-1">File path present but no view URL.</p>
                )}
                {!documentData.signedUrl && !documentData.filePath && status !== 'NOT_SUBMITTED' && status !== 'N/A' && (
                    <p className="text-xs text-gray-500 italic mb-1">Document submitted, processing...</p>
                )}
                {documentData.uploadedOn && (
                    <p className="text-xs text-gray-500">Uploaded: {formatDate(documentData.uploadedOn)}</p>
                )}
                {documentData.verifiedOrRejectedOn && (
                    <p className="text-xs text-gray-500">Processed: {formatDate(documentData.verifiedOrRejectedOn)}</p>
                )}
            </div>
            {documentData.verificationNotes && (
                <p className="text-xs text-red-600 mt-2 pt-1 border-t border-dashed">Notes: {documentData.verificationNotes}</p>
            )}
        </div>
    );
};

// Utility to safely get nested properties (replace with lodash.get or similar if available)
const getNested = (obj, path, defaultValue = undefined) => {
    const properties = path.split('.');
    return properties.reduce((acc, key) => (acc && acc[key] !== undefined && acc[key] !== null ? acc[key] : defaultValue), obj);
};


// Utility to format dates (replace with a robust library like date-fns or moment if available)
const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return 'Invalid Date';
        // Simple formatting, adjust as needed
        const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
        return date.toLocaleDateString('en-GB', options);
    } catch (e) {
        if (e) {
            console.log(e);
        }
        return 'Invalid Date';
    }
};


// --- Main Component ---

const KycDetailsViewModal = ({ show, user, onClose, activeTabState }) => {
    const [activeTab, setActiveTab] = activeTabState;

    useEffect(() => {
        // Default to a relevant tab when opening
        if (show) {
            setActiveTab('userDetails'); // Start with user details
        }
    }, [show, setActiveTab]);

    if (!show || !user) {
        return null;
    }

    console.log("USER HERE", user);

    // Define document field mappings based on the ROOT user object structure
    const rootBusinessDocFields = [
        { key: 'commercialRegistration', label: 'Commercial Registration (CR)' },
        { key: 'tradeLicense', label: 'Trade License' },
        { key: 'taxCard', label: 'Tax Card' },
        { key: 'establishmentCard', label: 'Establishment Card' },
        { key: 'memorandumOfAssociation', label: 'Memorandum of Association (MOA)' },
        { key: 'articleOfAssociation', label: 'Articles of Association (AOA)' },
        { key: 'otherDocument', label: 'Additional Document 1' },
        { key: 'otherDocumentTwo', label: 'Additional Document 2' },
        { key: 'otherDocument3', label: 'Additional Document 3' },
        { key: 'otherDocument4', label: 'Additional Document 4' },
        { key: 'otherDocument5', label: 'Additional Document 5' },
        { key: 'otherDocument6', label: 'Additional Document 6' },
        { key: 'otherDocument7', label: 'Additional Document 7' },
        { key: 'otherDocument8', label: 'Additional Document 8' },
        { key: 'otherDocument9', label: 'Additional Document 9' },
        { key: 'otherDocument10', label: 'Additional Document 10' },
        // Add invoiceAdditionalDoc1/2 if they belong here conceptually
        // { key: 'invoiceAdditionalDoc1', label: 'Invoice Additional Doc 1' },
        // { key: 'invoiceAdditionalDoc2', label: 'Invoice Additional Doc 2' },
    ];

    const rootFinancialDocFields = [
        { key: 'bankStatement', label: 'Bank Statements' },
        { key: 'auditedFinancialReport', label: 'Audited Financial Report' },
        { key: 'commercialCreditReport', label: 'Commercial Credit Report (CCR)' },
        { key: 'cashFlowLedger', label: 'Cash Flow Ledger / Mgmt Accounts' }, // Labelled as Ledger in data
        { key: 'cashFlowDocument', label: 'Cash Flow Statement Doc' }, // Raw statement upload
    ];

    // Note: Primary Applicant QID/Passport/Utility bill are now under KYC Details Tab

    // Define tabs with more descriptive names
    const tabs = [
        { key: 'userDetails', label: 'User & KYC Details' },
        { key: 'businessDetails', label: 'Business Details' },
        { key: 'businessDocs', label: 'Business Documents' },
        { key: 'financialDocs', label: 'Financial Documents' },
        { key: 'shareholders', label: 'Shareholders' },
        // { key: 'directorsSignatories', label: 'Directors & Signatories' }, // Combine if needed
        { key: 'buyers', label: 'Top Buyers' },
        { key: 'verification', label: 'Overall Verification' },
    ];

    const getOverallKycStatus = () => {
        const status = String(getNested(user, 'kyc.verificationStatus', 'N/A')).toUpperCase();
        return <StatusBadge status={status} />;
    };

    const getShareholderStatusClass = (status) => {
        const upperStatus = String(status || 'INITIATED').toUpperCase();
        if (['APPROVED', 'VERIFIED'].includes(upperStatus)) { return 'bg-green-100 text-green-800'; }
        else if (upperStatus === 'REJECTED') { return 'bg-red-100 text-red-800'; }
        else if (['PENDING', 'UNDER_REVIEW', 'INFO_NEEDED', 'SUBMITTED'].includes(upperStatus)) { return 'bg-yellow-100 text-yellow-800'; }
        else { return 'bg-blue-100 text-blue-800'; } // Default for INITIATED etc.
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-start z-50 p-4 pt-10 overflow-y-auto">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">

                {/* Modal Header */}
                <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10 flex-shrink-0">
                    <h2 className="text-xl font-semibold text-gray-800">
                        User Details: {getNested(user, 'firstName', '')} {getNested(user, 'lastName', '')}
                        <span className="text-sm text-gray-500 ml-2">(ID: {user._id})</span>
                    </h2>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors duration-150"
                        aria-label="Close Details Modal"
                    >
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /></svg>
                    </button>
                </div>

                {/* Modal Content Area (Scrollable) */}
                <div className="flex-grow overflow-y-auto">
                    {/* Tab Navigation */}
                    <div className="px-6 border-b border-gray-200 sticky top-0 bg-white z-[9]">
                        <nav className="-mb-px flex space-x-4 overflow-x-auto" aria-label="User Detail Tabs">
                            {tabs.map(tab => (
                                <button
                                    key={tab.key}
                                    className={`whitespace-nowrap py-3 px-2 border-b-2 font-medium text-sm transition-colors duration-150 ${activeTab === tab.key ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                                    onClick={() => setActiveTab(tab.key)}
                                >
                                    {tab.label}
                                </button>
                            ))}
                        </nav>
                    </div>

                    {/* Tab Content Display Area */}
                    <div className="p-4 sm:p-6 bg-gray-50">

                        {/* Tab: User & KYC Details */}
                        {activeTab === 'userDetails' && (
                            <div className="space-y-6">
                                {/* Basic User Info */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Primary User Information</h3>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                                        <RenderInfoItemViewOnly label="First Name" value={getNested(user, 'firstName')} />
                                        <RenderInfoItemViewOnly label="Middle Name" value={getNested(user, 'middleName')} />
                                        <RenderInfoItemViewOnly label="Last Name" value={getNested(user, 'lastName')} />
                                        <RenderInfoItemViewOnly label="Email" value={getNested(user, 'email')} />
                                        <RenderInfoItemViewOnly label="Mobile Number" value={getNested(user, 'mobileNo')} />
                                        <RenderInfoItemViewOnly label="Account Status" value={getNested(user, 'isActive')} />
                                    </div>
                                </div>

                                {/* KYC Personal Address */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">KYC - Personal Address</h3>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                                        <RenderInfoItemViewOnly label="Address Line 1" value={getNested(user, 'kyc.addressLine1')} />
                                        <RenderInfoItemViewOnly label="Address Line 2" value={getNested(user, 'kyc.addressLine2')} />
                                        <RenderInfoItemViewOnly label="City" value={getNested(user, 'kyc.city')} />
                                        <RenderInfoItemViewOnly label="State" value={getNested(user, 'kyc.state')} />
                                        <RenderInfoItemViewOnly label="Postal Code" value={getNested(user, 'kyc.postalCode')} />
                                        <RenderInfoItemViewOnly label="Country" value={getNested(user, 'kyc.country')} />
                                    </div>
                                </div>


                                {/* KYC Income Details */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">KYC - Income / Bank Details</h3>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                                        <RenderInfoItemViewOnly label="Account Number" value={getNested(user, 'kyc.incomeDetails.accountNumber')} />
                                        <RenderInfoItemViewOnly label="IBAN" value={getNested(user, 'kyc.incomeDetails.ifscCode')} />
                                        {/* Add other income details if available */}
                                    </div>
                                </div>

                                {/* KYC Employment Details (if any) */}
                                {Object.keys(getNested(user, 'kyc.employmentDetails', {})).length > 0 && (
                                    <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                        <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Bank Details</h3>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                                            {/* Map through employmentDetails keys dynamically or add specific fields */}
                                            <RenderInfoItemViewOnly label="Employer Name" value={getNested(user, 'kyc.employmentDetails.employerName')} />
                                            <RenderInfoItemViewOnly label="Position" value={getNested(user, 'kyc.employmentDetails.position')} />
                                            {/* ... etc */}
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}

                        {/* Tab: Business Details */}
                        {activeTab === 'businessDetails' && (
                            <div className="space-y-6">
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Business Information</h3>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                                        <RenderInfoItemViewOnly label="Business Name" value={getNested(user, 'kyc.businessDetails.businessName')} />
                                        <RenderInfoItemViewOnly label="Legal Entity Name" value={getNested(user, 'kyc.businessDetails.legalEntityName')} />
                                        <RenderInfoItemViewOnly label="Establishment Name" value={getNested(user, 'kyc.businessDetails.establishmentName')} />
                                        <RenderInfoItemViewOnly label="Legal Form" value={getNested(user, 'kyc.businessDetails.legalForm')} />
                                        <RenderInfoItemViewOnly label="Ownership Type" value={getNested(user, 'kyc.businessDetails.ownershipType')} />
                                        <RenderInfoItemViewOnly label="Sector" value={getNested(user, 'kyc.businessDetails.sector')} />
                                        <RenderInfoItemViewOnly label="Firm Nationality" value={getNested(user, 'kyc.businessDetails.firmNationality')} />
                                        <RenderInfoItemViewOnly label="CR Number" value={getNested(user, 'kyc.businessDetails.crNumber')} />
                                        <RenderInfoItemViewOnly label="CR Issue Date" value={formatDate(getNested(user, 'kyc.businessDetails.crIssueDate'))} />
                                        <RenderInfoItemViewOnly label="CR Expiry Date" value={formatDate(getNested(user, 'kyc.businessDetails.crExpiryDate'))} />
                                        <RenderInfoItemViewOnly label="Trade License (TL) Number" value={getNested(user, 'licenseNumber')} /> {/* From root */}
                                        <RenderInfoItemViewOnly label="TL Issue Date" value={formatDate(getNested(user, 'kyc.businessDetails.tlIssueDate'))} />
                                        <RenderInfoItemViewOnly label="TL Expiry Date" value={formatDate(getNested(user, 'kyc.businessDetails.tlExpiryDate'))} />
                                        <RenderInfoItemViewOnly label="Tax Reg No (TRN)" value={getNested(user, 'kyc.businessDetails.taxRegNo')} />
                                        <RenderInfoItemViewOnly label="TIN Number" value={getNested(user, 'kyc.businessDetails.tinNumber')} />
                                        <RenderInfoItemViewOnly label="Establishment ID" value={getNested(user, 'kyc.businessDetails.establishmentId')} />
                                        <RenderInfoItemViewOnly label="Establishment ID Issue" value={formatDate(getNested(user, 'kyc.businessDetails.establishmentIdIssueDate'))} />
                                        <RenderInfoItemViewOnly label="Establishment ID Expiry" value={formatDate(getNested(user, 'kyc.businessDetails.establishmentIdExpiryDate'))} />
                                        <RenderInfoItemViewOnly label="Branch Count" value={getNested(user, 'kyc.businessDetails.branchCount')} />
                                        {/* Add any other relevant business details from kyc.businessDetails */}
                                        {/* Example: <RenderInfoItemViewOnly label="Industry" value={getNested(user, 'kyc.businessDetails.industry')} /> */}
                                        {/* Example: <RenderInfoItemViewOnly label="Operating Years" value={getNested(user, 'kyc.businessDetails.operatingYears')} /> */}
                                    </div>
                                </div>

                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Business Address</h3>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                                        <RenderInfoItemViewOnly label="Address Line 1" value={getNested(user, 'kyc.businessDetails.businessAddressLine1')} />
                                        <RenderInfoItemViewOnly label="Address Line 2" value={getNested(user, 'kyc.businessDetails.businessAddressLine2')} />
                                        <RenderInfoItemViewOnly label="City" value={getNested(user, 'kyc.businessDetails.businessCity')} />
                                        <RenderInfoItemViewOnly label="Country" value={getNested(user, 'kyc.businessDetails.businessCountry')} />
                                        {/* Add Zone, Street, Building etc. if they exist under kyc.businessDetails */}
                                    </div>
                                </div>
                            </div>
                        )}


                        {/* Tab: Business Documents */}
                        {activeTab === 'businessDocs' && (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {rootBusinessDocFields.map(field => (
                                    <RenderDocumentDisplayViewOnly
                                        key={field.key}
                                        // Access document object directly from the root user object
                                        documentData={getNested(user, field.key, null)}
                                        label={field.label}
                                    />
                                ))}
                                {rootBusinessDocFields.every(field => !getNested(user, field.key)) && <p className="text-gray-500 italic md:col-span-3">No core business documents uploaded.</p>}
                            </div>
                        )}

                        {/* Tab: Financial Documents */}
                        {activeTab === 'financialDocs' && (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {rootFinancialDocFields.map(field => (
                                    <RenderDocumentDisplayViewOnly
                                        key={field.key}
                                        // Access document object directly from the root user object
                                        documentData={getNested(user, field.key, null)}
                                        label={field.label}
                                    />
                                ))}
                                {rootFinancialDocFields.every(field => !getNested(user, field.key)) && <p className="text-gray-500 italic md:col-span-3">No financial documents uploaded.</p>}
                            </div>
                        )}

                        {/* Tab: Shareholders */}
                        {activeTab === 'shareholders' && (
                            <div className="space-y-6">
                                {(getNested(user, 'shareholders', []) || []).map((sh, index) => (
                                    <div key={sh?._id || index} className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                        <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2 flex justify-between items-center">
                                            <span>Shareholder {index + 1}: {getNested(sh, 'firstName', '')} {getNested(sh, 'lastName', '')}</span>
                                            <span className={`px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full ${getShareholderStatusClass(getNested(sh, 'kycVerificationStatus'))}`}>
                                                KYC: {String(getNested(sh, 'kycVerificationStatus', 'INITIATED')).toUpperCase()}
                                            </span>
                                        </h3>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-4">
                                            <RenderInfoItemViewOnly label="Middle Name" value={getNested(sh, 'middleName')} />
                                            <RenderInfoItemViewOnly label="Email" value={getNested(sh, 'email')} />
                                            <RenderInfoItemViewOnly label="Zone" value={getNested(sh, 'address.zone')} />
                                            <RenderInfoItemViewOnly label="Street" value={getNested(sh, 'address.streetNo')} />
                                            <RenderInfoItemViewOnly label="Building" value={getNested(sh, 'address.buildingNo')} />
                                            <RenderInfoItemViewOnly label="Floor" value={getNested(sh, 'address.floorNo')} />
                                            <RenderInfoItemViewOnly label="Unit" value={getNested(sh, 'address.unitNo')} />
                                            {/* <RenderInfoItemViewOnly label="Landmark" value={getNested(sh, 'address.additionalLandmark')} /> */}
                                            <RenderInfoItemViewOnly label="Added On" value={formatDate(getNested(sh, 'addedOn'))} />
                                            <RenderInfoItemViewOnly label="Modified On" value={formatDate(getNested(sh, 'modifiedOn'))} />
                                            {/* Add shareholding percentage if available */}
                                            {/* <RenderInfoItemViewOnly label="Shareholding %" value={getNested(sh, 'shareholdingPercentage') !== undefined ? `${getNested(sh, 'shareholdingPercentage')}%` : undefined} /> */}
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-3 border-t">
                                            <RenderDocumentDisplayViewOnly documentData={getNested(sh, 'passport', null)} label="Passport" />
                                            <RenderDocumentDisplayViewOnly documentData={getNested(sh, 'qid', null)} label="QID" />
                                            <RenderDocumentDisplayViewOnly documentData={getNested(sh, 'proofOfAddress', null)} label="Proof of Address" />
                                        </div>
                                        {/* {getNested(sh, 'passport.verificationNotes') && <p className="text-xs text-red-600 mt-2">Passport Notes: {getNested(sh, 'passport.verificationNotes')}</p>}
                                        {getNested(sh, 'qid.verificationNotes') && <p className="text-xs text-red-600 mt-1">QID Notes: {getNested(sh, 'qid.verificationNotes')}</p>}
                                        {getNested(sh, 'proofOfAddress.verificationNotes') && <p className="text-xs text-red-600 mt-1">Proof of Address Notes: {getNested(sh, 'proofOfAddress.verificationNotes')}</p>} */}
                                    </div>
                                ))}
                                {/* {(getNested(user, 'kyc.shareholders', []) || []).length === 0 && <p className="text-gray-500 italic mt-4">No shareholders listed.</p>} */}
                            </div>
                        )}

                        {/* Tab: Directors & Signatories */}
                        {activeTab === 'directorsSignatories' && (
                            <div className="space-y-6">
                                {/* Directors */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Directors</h3>
                                    {(getNested(user, 'kyc.directors', []) || []).length > 0 ? (
                                        <div className="space-y-4">
                                            {(user.kyc.directors || []).map((dir, index) => (
                                                <div key={dir._id || index} className="border rounded p-3 bg-gray-50">
                                                    <p className="text-base font-medium text-gray-800 mb-2">Director {index + 1}: {getNested(dir, 'directorName')}</p>
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-3">
                                                        <RenderInfoItemViewOnly label="Position" value={getNested(dir, 'position')} />
                                                        <RenderInfoItemViewOnly label="Nationality" value={getNested(dir, 'nationality')} />
                                                        <RenderInfoItemViewOnly label="DOB" value={formatDate(getNested(dir, 'dateOfBirth'))} />
                                                        <RenderInfoItemViewOnly label="National ID" value={getNested(dir, 'nationalId')} />
                                                        <RenderInfoItemViewOnly label="Address" value={getNested(dir, 'directorAddress')} />
                                                        {/* Add UBO, Signatory flags if present */}
                                                    </div>
                                                    {/* Add document display if director object contains documents */}
                                                    {/* {getNested(dir, 'idDocument') && ( ... <RenderDocumentDisplayViewOnly ... /> )} */}
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="text-gray-500 italic">No directors listed.</p>
                                    )}
                                </div>

                                {/* Authorized Signatories */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Authorized Signatories</h3>
                                    {(getNested(user, 'kyc.authorizedSignatories', []) || []).length > 0 ? (
                                        <div className="space-y-4">
                                            {(user.kyc.authorizedSignatories || []).map((sig, index) => (
                                                <div key={sig._id || index} className="border rounded p-3 bg-gray-50">
                                                    <p className="text-base font-medium text-gray-800 mb-2">Signatory {index + 1}: {getNested(sig, 'name')}</p>
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-3">
                                                        <RenderInfoItemViewOnly label="Position" value={getNested(sig, 'position')} />
                                                        <RenderInfoItemViewOnly label="Contact" value={getNested(sig, 'contactNumber')} />
                                                    </div>
                                                    {/* Add document display if signatory object contains documents */}
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="text-gray-500 italic">No authorized signatories listed.</p>
                                    )}
                                </div>

                                {/* Beneficial Owners */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Beneficial Owners (UBOs)</h3>
                                    {(getNested(user, 'kyc.beneficialOwners', []) || []).length > 0 ? (
                                        <div className="space-y-4">
                                            {(user.kyc.beneficialOwners || []).map((owner, index) => (
                                                <div key={owner._id || index} className="border rounded p-3 bg-gray-50">
                                                    <p className="text-base font-medium text-gray-800 mb-2">Owner {index + 1}: {getNested(owner, 'name')}</p>
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-3">
                                                        <RenderInfoItemViewOnly label="Ownership %" value={getNested(owner, 'ownership') !== undefined ? `${getNested(owner, 'ownership')}%` : 'N/A'} />
                                                        {/* Add other UBO details */}
                                                    </div>
                                                    {/* Add document display if owner object contains documents */}
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="text-gray-500 italic">No beneficial owners listed.</p>
                                    )}
                                </div>
                            </div>
                        )}


                        {/* Tab: Top Buyers */}
                        {activeTab === 'buyers' && (
                            <div className="space-y-4">
                                {(getNested(user, 'kyc.buyers', []) || []).map((buyer, index) => (
                                    <div key={buyer?._id || index} className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                        <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">
                                            Buyer {index + 1}: {getNested(buyer, 'buyerName', 'N/A')}
                                        </h3>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-3">
                                            <RenderInfoItemViewOnly label="Contact Person" value={getNested(buyer, 'contactPerson')} />
                                            <RenderInfoItemViewOnly label="Contact Phone" value={getNested(buyer, 'contactPhone')} />
                                            <RenderInfoItemViewOnly label="Contact Email" value={getNested(buyer, 'contactEmail')} />
                                            <RenderInfoItemViewOnly label="Registration Number" value={getNested(buyer, 'registrationNumber')} />
                                            {/* <RenderInfoItemViewOnly label="Business Type" value={getNested(buyer, 'businessType')} /> */}
                                            {/* <RenderInfoItemViewOnly label="Payment Terms" value={getNested(buyer, 'paymentTerms')} /> */}
                                            {/* <RenderInfoItemViewOnly label="Address" value={getNested(buyer, 'buyerAddress')} /> */}
                                        </div>
                                        {/* Add buyer document if it exists */}
                                        {/* {getNested(buyer, 'companyDocument') && ( ... <RenderDocumentDisplayViewOnly ... /> )} */}
                                    </div>
                                ))}
                                {(getNested(user, 'kyc.buyers', []) || []).length === 0 && <p className="text-gray-500 italic">No buyers listed.</p>}
                            </div>
                        )}

                        {/* Tab: Overall Verification */}
                        {activeTab === 'verification' && (
                            <div className="space-y-6">
                                <div className="bg-white p-6 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Overall KYC Verification</h3>
                                    <div className="space-y-3">
                                        <div className="py-1 break-words text-sm">
                                            <span className="font-medium text-gray-600">Overall KYC Status: </span>
                                            {getOverallKycStatus()}
                                        </div>
                                        <RenderInfoItemViewOnly label="Verified/Processed On" value={formatDate(getNested(user, 'kyc.verifiedOn'))} />
                                        <RenderInfoItemViewOnly label="Overall Verification Notes" value={getNested(user, 'kyc.verificationNotes', '')} />
                                        {/* Add verifiedBy, rejectionReason if available */}
                                        {/* <RenderInfoItemViewOnly label="Verified By" value={getNested(user, 'kyc.verifiedBy', 'N/A')} /> */}
                                        {/* <RenderInfoItemViewOnly label="Rejection Reason" value={getNested(user, 'kyc.rejectionReason')} /> */}
                                    </div>
                                </div>
                            </div>
                        )}

                    </div> {/* End Tab Content Area */}
                </div> {/* End Modal Content Area (Scrollable) */}


                {/* Modal Footer (Only Close Button) */}
                <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end sticky bottom-0 flex-shrink-0">
                    <button
                        onClick={onClose}
                        type="button"
                        className="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        Close
                    </button>
                </div>
            </div> {/* End Modal Container */}
        </div> // End Modal Backdrop
    );
};

// =========================================================================
//          MAIN CREDIT ASSESSMENT PAGE COMPONENT (Uses Tailwind)
// =========================================================================
export default function CreditAssessmentPage() {
    // --- State Variables ---
    const [assessments, setAssessments] = useState([]); // Holds ALL raw fetched CreditLine data
    const [offers, setOffers] = useState([]); // Holds all raw fetched creditLineOffers
    const [usersMap, setUsersMap] = useState(new Map()); // Holds fetched user data keyed by ID

    console.log(assessments, offers, usersMap);

    // Filtered Data States for Tabs
    const [applicationAssessments, setApplicationAssessments] = useState([]); // Tab 1
    const [offerAssessments, setOfferAssessments] = useState([]);           // Tab 2
    const [rejectedAssessments, setRejectedAssessments] = useState([]);       // Tab 3

    // Modal States
    const [selectedAssessmentForOffer, setSelectedAssessmentForOffer] = useState(null);
    const [selectedUserForKycModal, setSelectedUserForKycModal] = useState(null);
    const [selectedAssessmentForRejection, setSelectedAssessmentForRejection] = useState(null);

    const [showOfferModal, setShowOfferModal] = useState(false);
    const [showKycDetailsModal, setShowKycDetailsModal] = useState(false);
    const [showRejectionModal, setShowRejectionModal] = useState(false);
    const [isExportingExcel, setIsExportingExcel] = useState(false); // <-- ADD THIS

    // Loading & Error States
    const [loading, setLoading] = useState(true);
    const [fetchError, setFetchError] = useState(null);

    // Top Level Tab State
    const [activeTopLevelTab, setActiveTopLevelTab] = useState('applications'); // 'applications', 'creditOffers', 'rejected'

    // Offer Modal State
    const [isSubmittingOffer, setIsSubmittingOffer] = useState(false);
    const [offerData, setOfferData] = useState({
        creditLimit: '', tenureDays: '', interestRate: '', processingFeeValue: '',
        processingFeeType: 'flat', riskProfile: 'MEDIUM', notes: '',
    });

    // Rejection Modal State
    const [rejectionReason, setRejectionReason] = useState('');
    const [isSubmittingRejection, setIsSubmittingRejection] = useState(false);

    // KYC View Modal State
    const [activeKycModalTab, setActiveKycModalTab] = useState('businessDocs');

    // --- State for Filters ---
    // --- State for Filters ---
    const [assessmentFilters, setAssessmentFilters] = useState({
        searchTerm: '',
        startDate: '', // Applied Date Start
        endDate: '',   // Applied Date End
        minOfferLimit: '', // New: Offer Credit Limit Min
        maxOfferLimit: '', // New: Offer Credit Limit Max
        offerStatus: '',   // New: Offer Status (from InvoiceFinancingOffers status enum)
        creditLineStatus: '', // New: Credit Line Status (from InvoiceFinancingCreditLine status enum)
        kycStatus: '',      // New: KYC Status (from user kyc.verificationStatus)
    });

    const resetAssessmentFilters = useCallback(() => {
        setAssessmentFilters({
            searchTerm: '',
            startDate: '',
            endDate: '',
            minOfferLimit: '', // Reset new filters
            maxOfferLimit: '', // Reset new filters
            offerStatus: '',   // Reset new filters
            creditLineStatus: '', // Reset new filters
            kycStatus: '',      // Reset new filters
        });
    }, []);

    // --- Fee Calculation Helpers ---
    const calculateMaxAllowedFee = useCallback((creditLimit) => {
        const limit = Number(creditLimit) || 0;
        if (limit <= 0) return MAX_PROCESSING_FEE_FLAT;
        const percentageMax = (MAX_PROCESSING_FEE_PERCENTAGE / 100) * limit;
        return Math.min(MAX_PROCESSING_FEE_FLAT, percentageMax) + 999999999;
    }, []);

    const calculateCurrentFee = useCallback((type, value, creditLimit) => {
        const feeVal = Number(value) || 0;
        const limitVal = Number(creditLimit) || 0;
        if (type === 'flat') return feeVal;
        if (type === 'percentage') return (feeVal / 100) * limitVal;
        return 0;
    }, []);

    // --- Apply Filters to Tab Data ---
    const filteredApplicationAssessments = useMemo(() => {

        // Destructure filters
        const { searchTerm, startDate, endDate, creditLineStatus, kycStatus } = assessmentFilters; // Add other filters if used here
        const lowerSearchTerm = searchTerm.toLowerCase();
        const tsStartDate = startDate ? new Date(startDate).setHours(0, 0, 0, 0) : null;
        const tsEndDate = endDate ? new Date(endDate).setHours(23, 59, 59, 999) : null;

        return applicationAssessments.filter(assessment => {
            // --- UNCOMMENTED & ADJUSTED ---
            const borrowerName = `${getNested(assessment, 'userDetails.firstName', '')} ${getNested(assessment, 'userDetails.lastName', '')}`.toLowerCase();
            const businessName = getNested(assessment, 'userDetails.kyc.businessDetails.businessName', '').toLowerCase();
            const appliedAtTs = assessment.createdAt ? new Date(assessment.createdAt).getTime() : null;
            const currentCreditLineStatus = getNested(assessment, 'creditLineStatus', ''); // Use CL status from the assessment object
            const currentKycStatus = getNested(assessment, 'kycStatus', ''); // Use KYC status from the assessment object

            // Apply Filters
            if (lowerSearchTerm && !(borrowerName.includes(lowerSearchTerm) || businessName.includes(lowerSearchTerm))) return false;
            if (tsStartDate && (!appliedAtTs || appliedAtTs < tsStartDate)) return false;
            if (tsEndDate && (!appliedAtTs || appliedAtTs > tsEndDate)) return false;
            if (creditLineStatus && currentCreditLineStatus !== creditLineStatus) return false;
            if (kycStatus && currentKycStatus !== kycStatus) return false;
            // --- END UNCOMMENTED ---

            // Removed console.log(assessment)
            return true; // Passed all filters
        });
    }, [applicationAssessments, assessmentFilters]);

    const addDocumentFieldsToRow = (rowData, docObject, prefix, getNestedFn, formatDateFn) => {
        if (!docObject) {
            rowData[`${prefix} - File Name`] = 'N/A';
            rowData[`${prefix} - Uploaded On`] = 'N/A';
            rowData[`${prefix} - Status`] = 'NOT_UPLOADED';
            rowData[`${prefix} - Notes`] = '';
            rowData[`${prefix} - Verification Date`] = 'N/A';
            rowData[`${prefix} - Signed URL`] = 'N/A';
            rowData[`${prefix} - MIME Type`] = 'N/A';
            return;
        }
        const filePath = getNestedFn(docObject, 'filePath', '');
        rowData[`${prefix} - File Name`] = filePath ? filePath.split('/').pop() : 'N/A';
        rowData[`${prefix} - Uploaded On`] = formatDateFn(getNestedFn(docObject, 'uploadedOn'));
        rowData[`${prefix} - Status`] = getNestedFn(docObject, 'verificationStatus', 'PENDING');
        rowData[`${prefix} - Notes`] = getNestedFn(docObject, 'verificationNotes', '');
        rowData[`${prefix} - Verification Date`] = formatDateFn(getNestedFn(docObject, 'verifiedOrRejectedOn'));
        rowData[`${prefix} - Signed URL`] = getNestedFn(docObject, 'signedUrl', 'N/A');
        rowData[`${prefix} - MIME Type`] = getNestedFn(docObject, 'mimeType', 'N/A'); // From RenderDocumentDisplayViewOnly
    };

    const filteredOfferAssessments = useMemo(() => {
        // Destructure *all* filters
        const { searchTerm, startDate, endDate, minOfferLimit, maxOfferLimit, offerStatus, creditLineStatus, kycStatus } = assessmentFilters;
        const lowerSearchTerm = searchTerm.toLowerCase();
        const tsStartDate = startDate ? new Date(startDate).setHours(0, 0, 0, 0) : null;
        const tsEndDate = endDate ? new Date(endDate).setHours(23, 59, 59, 999) : null;
        const numMinOfferLimit = minOfferLimit === '' ? -Infinity : parseFloat(minOfferLimit);
        const numMaxOfferLimit = maxOfferLimit === '' ? Infinity : parseFloat(maxOfferLimit);

        return offerAssessments.filter(assessment => {
            // --- UNCOMMENTED & ADJUSTED ---
            const borrowerName = `${getNested(assessment, 'userDetails.firstName', '')} ${getNested(assessment, 'userDetails.lastName', '')}`.toLowerCase();
            const businessName = getNested(assessment, 'userDetails.kyc.businessDetails.businessName', '').toLowerCase();
            const appliedAtTs = assessment.createdAt ? new Date(assessment.createdAt).getTime() : null;

            // Offer details should exist in this tab (either pending or accepted/active)
            const offerToShow = assessment.acceptedOrActiveOfferDetails || assessment.pendingOfferDetails; // Get the relevant offer
            const offerLimit = parseFloat(getNested(offerToShow, 'creditLimit', NaN));
            const currentOfferStatusFromOffer = getNested(offerToShow, 'status', ''); // Get status directly from the offer object
            const currentCreditLineStatus = getNested(assessment, 'creditLineStatus', '');
            const currentKycStatus = getNested(assessment, 'kycStatus', '');

            // --- Apply Filters ---
            if (lowerSearchTerm && !(borrowerName.includes(lowerSearchTerm) || businessName.includes(lowerSearchTerm))) return false;
            if (tsStartDate && (!appliedAtTs || appliedAtTs < tsStartDate)) return false;
            if (tsEndDate && (!appliedAtTs || appliedAtTs > tsEndDate)) return false;

            // Offer Limit Check
            if (!isNaN(numMinOfferLimit) && (isNaN(offerLimit) || offerLimit < numMinOfferLimit)) { return false; }
            if (!isNaN(numMaxOfferLimit) && (isNaN(offerLimit) || offerLimit > numMaxOfferLimit)) { return false; }

            // Offer Status Check (compare against the actual offer status)
            if (offerStatus && currentOfferStatusFromOffer !== offerStatus) return false;

            // Credit Line Status Check
            if (creditLineStatus && currentCreditLineStatus !== creditLineStatus) return false;

            // KYC Status Check
            if (kycStatus && currentKycStatus !== kycStatus) return false;
            // --- END UNCOMMENTED ---

            // Removed console.log(assessment)
            return true;
        });
    }, [offerAssessments, assessmentFilters]);

    const filteredRejectedAssessments = useMemo(() => {
        // Apply relevant filters (Search, Date, maybe CL Status, KYC Status)
        const { searchTerm, startDate, endDate, creditLineStatus, kycStatus } = assessmentFilters; // Offer limit/status likely irrelevant here
        const lowerSearchTerm = searchTerm.toLowerCase();
        const tsStartDate = startDate ? new Date(startDate).setHours(0, 0, 0, 0) : null;
        const tsEndDate = endDate ? new Date(endDate).setHours(23, 59, 59, 999) : null;

        return rejectedAssessments.filter(assessment => {
            // --- UNCOMMENTED & ADJUSTED ---
            const borrowerName = `${getNested(assessment, 'userDetails.firstName', '')} ${getNested(assessment, 'userDetails.lastName', '')}`.toLowerCase();
            const businessName = getNested(assessment, 'userDetails.kyc.businessDetails.businessName', '').toLowerCase();
            const appliedAtTs = assessment.createdAt ? new Date(assessment.createdAt).getTime() : null; // Or use rejectionDetails.date if filtering by rejection date
            const currentCreditLineStatus = getNested(assessment, 'creditLineStatus', ''); // Status before rejection might be relevant
            const currentKycStatus = getNested(assessment, 'kycStatus', '');

            // Apply Filters
            if (lowerSearchTerm && !(borrowerName.includes(lowerSearchTerm) || businessName.includes(lowerSearchTerm))) return false;
            if (tsStartDate && (!appliedAtTs || appliedAtTs < tsStartDate)) return false; // Filter by application date
            if (tsEndDate && (!appliedAtTs || appliedAtTs > tsEndDate)) return false; // Filter by application date
            // Add date filtering based on rejectionDetails.date if needed
            if (creditLineStatus && currentCreditLineStatus !== creditLineStatus) return false; // Filter by CL status *at time of rejection*
            if (kycStatus && currentKycStatus !== kycStatus) return false;
            // --- END UNCOMMENTED ---

            // Removed console.log(assessment)
            return true;
        });
    }, [rejectedAssessments, assessmentFilters]);
    // --- Define filterAndSetData using useCallback to stabilize its reference ---


    const filterAndSetData = useCallback((
        allFetchedCreditLines,
        userDetailsMap,
        allFetchedOffers,
        // Using the getNested defined above or passed in
        localGetNested = getNested
    ) => {
        const categories = {
            PENDING_OFFER: [],
            ACTIVE_OR_ACCEPTED: [],
            REJECTED: [],
            UNDER_REVIEW: [],
            OTHER: [],
        };
        console.log(`--- filterAndSetData START ---`);

        if (!allFetchedCreditLines || !userDetailsMap || !allFetchedOffers) {
            console.error("filterAndSetData received invalid input:", { allFetchedCreditLines, userDetailsMap, allFetchedOffers });
            return categories; // Return empty categories if input is bad
        }


        allFetchedCreditLines.forEach(cl => {
            // Ensure cl and cl.userId exist before proceeding
            if (!cl || !cl.userId) {
                console.warn("Skipping CL due to missing data:", cl);
                return;
            }

            const userDetails = userDetailsMap.get(cl.userId);
            // Log user matching
            // console.log(`CL_ID: ${cl._id}, User_ID: ${cl.userId}, UserDetails Found: ${!!userDetails}`);

            if (!userDetails) {
                // Decide how to handle: skip or categorize differently? Let's skip for now.
                console.warn(`No user details found for CL ${cl._id}, skipping categorization.`);
                return;
            }

            // Define variables *after* confirming userDetails exists
            const kycStatus = String(localGetNested(userDetails, 'kyc.verificationStatus', 'N/A')).toUpperCase();
            const clStatus = String(cl.creditLineStatus || 'UNKNOWN').toUpperCase(); // Overall status

            const userOffers = allFetchedOffers.filter(offer => offer.merchantId === cl.userId);

            const acceptedOrActiveOffer = userOffers.find(offer =>
                offer.status && ['ACCEPTED', 'LOAN_CONTRACT_ACCEPTED', 'INITIATED_FUND_TRANSFER', 'READY_FOR_DISBURSAL', 'LOAN_IN_PROGRESS', 'PAID', 'OVERDUE', 'DEFAULTED']
                    .includes(String(offer.status).toUpperCase())
            );

            const pendingOffer = userOffers.find(offer =>
                offer.status && String(offer.status).toUpperCase() === 'PENDING'
            );

            let latestRejection = null;
            if (Array.isArray(cl.reviewHistory) && cl.reviewHistory.length > 0) {
                const rejectionEntry = [...cl.reviewHistory].reverse().find(h => h.status === 'REJECTED');
                if (rejectionEntry) {
                    latestRejection = {
                        date: rejectionEntry.reviewDate || rejectionEntry.createdAt,
                        reason: (rejectionEntry.rejectionReasons?.length > 0 ? rejectionEntry.rejectionReasons.join(', ') : rejectionEntry.notes) || 'Reason not specified',
                        rejectedBy: String(rejectionEntry.reviewedBy)
                    };
                }
            }

            // Log status check data (Now all variables used should be defined)
            console.log(`CL ${cl._id} - Status Check: clStatus=${clStatus}, kycStatus=${kycStatus}, hasAccepted=${!!acceptedOrActiveOffer}, hasPending=${!!pendingOffer}, hasRejection=${!!latestRejection}`);

            const assessment = {
                ...cl,
                userDetails: userDetails,
                kycStatus: kycStatus, // Correctly uses the defined kycStatus
                allUserOffers: userOffers,
                acceptedOrActiveOfferDetails: acceptedOrActiveOffer,
                pendingOfferDetails: pendingOffer,
                rejectionDetails: latestRejection,
                displayStatus: clStatus, // Default display status
            };

            let categoryAssigned = false;

            // --- Categorization logic ---
            if (assessment.rejectionDetails) {
                console.log(`CL ${cl._id} -> REJECTED`);
                assessment.displayStatus = "REJECTED";
                categories.REJECTED.push(assessment);
                categoryAssigned = true;
            }

            // Check ACTIVE_OR_ACCEPTED using defined clStatus and assessment properties
            if (!categoryAssigned && (clStatus === 'ACTIVE' || assessment.acceptedOrActiveOfferDetails)) {
                console.log(`CL ${cl._id} -> ACTIVE_OR_ACCEPTED (clStatus=${clStatus}, hasAccepted=${!!assessment.acceptedOrActiveOfferDetails})`);
                if (assessment.acceptedOrActiveOfferDetails) {
                    assessment.displayStatus = `Offer ${assessment.acceptedOrActiveOfferDetails.status}`;
                } else if (clStatus === 'ACTIVE') {
                    assessment.displayStatus = "Credit Line Active";
                } // No else needed, displayStatus already defaults to clStatus
                categories.ACTIVE_OR_ACCEPTED.push(assessment);
                categoryAssigned = true;
            }

            // Check PENDING_OFFER using assessment property
            if (!categoryAssigned && assessment.pendingOfferDetails) {
                console.log(`CL ${cl._id} -> PENDING_OFFER`);
                assessment.displayStatus = `Offer ${assessment.pendingOfferDetails.status}`;
                categories.PENDING_OFFER.push(assessment);
                categoryAssigned = true;
            }

            // Check UNDER_REVIEW using defined clStatus and kycStatus
            // This version puts ALL 'UNDER_REVIEW' CLs here.
            // Change condition to '&& kycStatus === 'APPROVED'' if only approved KYC should be here.
            if (!categoryAssigned && clStatus === 'UNDER_REVIEW') {
                console.log(`CL ${cl._id} -> UNDER_REVIEW (kycStatus=${kycStatus})`);
                assessment.displayStatus = "Application Under Review"; // Or display kycStatus
                categories.UNDER_REVIEW.push(assessment);
                categoryAssigned = true;
            }

            if (!categoryAssigned) {
                console.log(`CL ${cl._id} -> OTHER`);
                // displayStatus already defaults to clStatus
                categories.OTHER.push(assessment);
            }
        });
        console.log(`--- filterAndSetData END ---`);
        return categories;
    }, []); // filterAndSetData likely doesn't need dependencies if it only uses its arguments

    // --- Data Fetching Function ---
    const fetchData = useCallback(async () => {
        console.log("fetchData triggered..."); // Log when fetchData starts
        setLoading(true);
        setFetchError(null);
        // Reset state before fetch
        setApplicationAssessments([]);
        setOfferAssessments([]);
        setRejectedAssessments([]);
        setAssessments([]);
        setOffers([]);
        setUsersMap(new Map());

        let fetchedCreditLines = []; // Define variables outside try for finally block access if needed
        let fetchedUsersMap = new Map();
        let fetchedOffers = [];

        try {
            // Parallel fetching using Promise.allSettled
            console.log("Starting API calls...");
            const [clResult, userResult, offerResult] = await Promise.allSettled([
                fetch(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLines`),
                fetch(`${config.apiUrl}/ops/invoiceFinancing/getSubmittedKycs`),
                axios.get(`${config.apiUrl}/ops/invoiceFinancing/offers?offerType=creditLineOffer`)
            ]);
            console.log("API calls finished.");

            // --- Process Credit Lines ---
            if (clResult.status === 'fulfilled' && clResult.value.ok) {
                const clData = await clResult.value.json(); // Get data first
                fetchedCreditLines = Array.isArray(clData) ? clData : (clData ? [clData] : []); // Ensure array
            } else if (clResult.status === 'fulfilled' && clResult.value.status === 404) {
                console.log("No credit lines found (404).");
                fetchedCreditLines = [];
            } else if (clResult.status === 'rejected' || !clResult.value.ok) {
                const errorReasonCL = clResult.status === 'rejected' ? clResult.reason : `Status ${clResult.value?.status}`;
                console.error(`Failed to fetch credit lines: ${errorReasonCL}`);
                // Consider setting fetchError here if critical
                // setFetchError(`Failed to fetch credit lines: ${errorReasonCL}`);
            }
            console.log("Processed Credit Lines:", fetchedCreditLines.length);

            // --- Process Users ---
            if (userResult.status === 'fulfilled' && userResult.value.ok) {
                const usersResJson = await userResult.value.json();
                if (usersResJson && usersResJson.success === true && Array.isArray(usersResJson.kycs)) {
                    fetchedUsersMap = new Map(usersResJson.kycs.map(user => [user._id, user]));
                } else {
                    console.warn("User KYC fetch successful but data format unexpected.", usersResJson);
                }
            } else if (userResult.status === 'fulfilled' && userResult.value.status === 404) {
                console.log("User KYC endpoint returned 404 (No users found).");
            } else if (userResult.status === 'rejected' || !userResult.value.ok) {
                const errorReasonUser = userResult.status === 'rejected' ? userResult.reason : `Status ${userResult.value?.status}`;
                console.error(`Failed to fetch users: ${errorReasonUser}`);
                // Consider setting fetchError here if critical
                // setFetchError(`Failed to fetch users: ${errorReasonUser}`);
            }
            console.log("Processed Users Map Size:", fetchedUsersMap.size);

            // --- Process Offers ---
            if (offerResult.status === 'fulfilled' && offerResult.value.data?.success && Array.isArray(offerResult.value.data.offers)) {
                fetchedOffers = offerResult.value.data.offers;
            } else if (offerResult.status === 'rejected' && axios.isAxiosError(offerResult.reason) && offerResult.reason.response?.status === 404) {
                console.log("No credit line offers found (404).");
            } else if (offerResult.status === 'rejected' || !offerResult.value.data?.success) {
                const errorReasonOffer = offerResult.status === 'rejected' ? offerResult.reason : `Success false or invalid data: ${JSON.stringify(offerResult.value.data)}`;
                console.warn(`Failed to fetch offers: ${errorReasonOffer}`);
            }
            console.log("Processed Offers:", fetchedOffers.length);

            // --- Set raw data state ---
            setAssessments(fetchedCreditLines);
            setOffers(fetchedOffers);
            setUsersMap(fetchedUsersMap);

            // --- Filter and set tab-specific data ---
            console.log("Calling filterAndSetData...");
            const categories = filterAndSetData(fetchedCreditLines, fetchedUsersMap, fetchedOffers, getNested); // Pass fetched data
            console.log("filterAndSetData returned:", JSON.stringify(categories)); // Log the result

            const sortLogic = (a, b) => {
                const statusA = getNested(a, 'kycStatus', 'UNKNOWN');
                const statusB = getNested(b, 'kycStatus', 'UNKNOWN');
                
                const orderA = CREDIT_LINE_STATUS_ORDER[statusA] ?? DEFAULT_CREDIT_LINE_STATUS_PRIORITY;
                const orderB = CREDIT_LINE_STATUS_ORDER[statusB] ?? DEFAULT_CREDIT_LINE_STATUS_PRIORITY;
        
                if (orderA !== orderB) {
                  return orderA - orderB;
                }
        
                const dateA = new Date(getNested(a, 'createdAt', 0));
                const dateB = new Date(getNested(b, 'createdAt', 0));
                const timeA = !isNaN(dateA.getTime()) ? dateA.getTime() : 0;
                const timeB = !isNaN(dateB.getTime()) ? dateB.getTime() : 0;
                return timeB - timeA;
              };
              
            // Map returned categories to the tab state variables
            setApplicationAssessments([...(categories.UNDER_REVIEW || [])].sort(sortLogic));
            const combinedOffers = [...(categories.PENDING_OFFER || []), ...(categories.ACTIVE_OR_ACCEPTED || [])];
            setOfferAssessments([...combinedOffers].sort(sortLogic));
            setRejectedAssessments([...(categories.REJECTED || [])].sort(sortLogic));  

            // Log counts *after* setting state attempt
            console.log("State Update Attempt - Applications:", (categories.UNDER_REVIEW || []).length);
            console.log("State Update Attempt - Offers:", (categories.PENDING_OFFER || []).length + (categories.ACTIVE_OR_ACCEPTED || []).length);
            console.log("State Update Attempt - Rejected:", (categories.REJECTED || []).length);
            console.log("State Update Attempt - Other:", (categories.OTHER || []).length);

        } catch (error) {
            console.error('Error during fetchData processing:', error);
            setFetchError(error.message || "An unknown error occurred during data processing.");
            // Ensure state is reset on processing error too
            setApplicationAssessments([]);
            setOfferAssessments([]);
            setRejectedAssessments([]);
            setAssessments([]);
            setOffers([]);
            setUsersMap(new Map());
        } finally {
            setLoading(false);
            console.log("Data fetch process finished.");
        }
        // Add filterAndSetData (which is memoized) as a dependency
    }, [filterAndSetData]); // Dependency array includes filterAndSetData

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    // --- Modal Opening Functions ---
    const handleViewKycDetails = useCallback((assessment) => {
        if (!assessment?.userDetails) {
            alert("Error: Could not load borrower details for KYC review."); return;
        }
        setSelectedUserForKycModal(assessment.userDetails);
        setActiveKycModalTab('businessDocs');
        setShowKycDetailsModal(true);
    }, []);

    // const handleOpenOfferModal = useCallback((assessment) => {
    //     if (!assessment?.userDetails?._id) {
    //         alert("Error: Could not load borrower details."); return;
    //     }
    //     // Validation checks (already done by filtering, but good safety net)
    //     if (String(getNested(assessment, 'kycStatus', '')).toUpperCase() !== 'APPROVED') {
    //         alert("KYC not approved. Cannot make offer."); return;
    //     }
    //     if (assessment.hasAcceptedOffer) {
    //         alert("An offer has already been accepted by the borrower."); return;
    //     }
    //     if (assessment.thisLenderOfferDetails) {
    //         alert("You have already submitted an offer for this application."); return;
    //     }

    //     setSelectedAssessmentForOffer(assessment);
    //     setOfferData({ // Reset form for a new offer
    //         creditLimit: '', tenureDays: '', interestRate: '', processingFeeValue: '',
    //         processingFeeType: 'flat', riskProfile: 'MEDIUM', notes: '',
    //     });
    //     setShowOfferModal(true);
    // }, []);

    // const handleOpenRejectionModal = useCallback((assessment) => {
    //     if (!assessment?._id || !assessment?.userId) {
    //         alert("Error: Missing assessment info for rejection."); return;
    //     }
    //     // Validation checks
    //     if (String(getNested(assessment, 'kycStatus', '')).toUpperCase() !== 'APPROVED') {
    //         alert("Cannot reject application before KYC is approved."); return; // Or adjust logic if rejection is allowed earlier
    //     }
    //     if (assessment.hasAcceptedOffer) {
    //         alert("An offer has already been accepted by the borrower. Cannot reject now."); return;
    //     }
    //     if (assessment.thisLenderOfferDetails) {
    //         alert("You have already submitted an offer. Cannot reject now."); return; // Prevent rejection if offer already made
    //     }
    //     setSelectedAssessmentForRejection(assessment);
    //     setRejectionReason('');
    //     setShowRejectionModal(true);
    // }, []);

    // --- Submission Functions ---
    const handleSubmitOffer = useCallback(async () => {
        const assessment = selectedAssessmentForOffer;
        // --- Start: Validation Checks ---
        if (!assessment || String(getNested(assessment, 'kycStatus', '')).toUpperCase() !== 'APPROVED' || assessment.hasAcceptedOffer || assessment.thisLenderOfferDetails) {
            alert('Cannot submit offer. Ensure KYC is approved, no offer is accepted, and you have not already submitted an offer.');
            return;
        }
        const lenderId = localStorage.getItem('userId');
        const borrowerUserId = getNested(assessment, 'userDetails._id');
        if (!borrowerUserId || !lenderId) {
            alert('Borrower/Lender ID missing.'); return;
        }
        const currentLimit = Number(offerData.creditLimit);
        const currentTenure = Number(offerData.tenureDays);
        const currentInterest = Number(offerData.interestRate);
        const currentFeeValue = Number(offerData.processingFeeValue);
        if (!(currentLimit > 0) || !(currentTenure > 0) || !(currentInterest >= 0) || !(currentFeeValue >= 0)) {
            alert('Please fill in Credit Limit (>0), Tenure (>0), Interest Rate (>=0), and Processing Fee (>=0) with valid numbers.'); return;
        }
        const currentFee = calculateCurrentFee(offerData.processingFeeType, currentFeeValue, currentLimit);
        const maxAllowed = calculateMaxAllowedFee(currentLimit);
        // Note: The maxAllowed calculation might still have the +999999999 from previous logs - ensure that's intended or remove it.
        if (currentFee > maxAllowed) {
            alert(`Processing Fee (QAR ${currentFee.toFixed(2)}) exceeds the maximum allowed (QAR ${maxAllowed.toFixed(2)}). Please adjust.`); return;
        }
        // --- End: Validation Checks ---

        // --- Close Modal Immediately and Set Loading ---
        setShowOfferModal(false);
        setSelectedAssessmentForOffer(null);
        setIsSubmittingOffer(true);
        // --- End Close Modal ---

        // Construct payload (ensure this matches backend expectations)
        const payload = {
            merchantId: borrowerUserId,
            lenderId: lenderId,
            offerType: "creditLineOffer",
            creditLimit: currentLimit,
            tenureDays: currentTenure,
            interestRate: String(currentInterest), // Ensure backend handles String if needed
            currency: 'QAR',
            processingFee: { type: offerData.processingFeeType, value: currentFeeValue },
            riskProfile: offerData.riskProfile || 'MEDIUM',
            notes: offerData.notes || '',
            status: 'PENDING' // Initial status
        };
        if (!payload.notes?.trim()) delete payload.notes;


        try {
            console.log("Attempting to submit offer...");
            const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/createOffer`, payload);
            console.log("Offer API Response:", response.data);

            // ***** CORRECTED SUCCESS CHECK *****
            // Check for the presence of the offerId instead of a 'success' boolean
            if (!response.data?.offerId) {
                // Throw error if backend didn't return the expected offerId
                // Use the message from the response if available, otherwise a generic error
                throw new Error(response.data?.message || "Failed to create offer: No offer ID received from backend.");
            }
            // ***** END CORRECTED CHECK *****


            // --- Success Path ---
            console.log("Offer submission successful. Refreshing data...");
            alert(`Offer submitted successfully.`); // Inform user

            // *** Call fetchData to refresh the page content ***
            await fetchData();
            // *** End Refresh Call ***

            console.log("Data refresh called after successful offer submission.");
            // --- End Success Path ---

        } catch (error) {
            // Log the actual error object for better debugging
            console.error('Error submitting offer:', error);
            // Display the error message from the caught error
            alert(`Error submitting offer: ${error.message || 'An unknown error occurred'}`);
        } finally {
            // Always reset loading state
            setIsSubmittingOffer(false);
            console.log("Offer submission process finished (finally block).");
        }
    }, [
        selectedAssessmentForOffer,
        offerData,
        fetchData, // fetchData dependency is correct
        calculateCurrentFee,
        calculateMaxAllowedFee,
        setShowOfferModal,
        setIsSubmittingOffer,
        setSelectedAssessmentForOffer
    ]); // Removed filterAndSetData as it's handled within fetchData

    const handleSubmitRejection = useCallback(async () => {
        if (!selectedAssessmentForRejection || !rejectionReason.trim()) {
            alert('Please provide a rejection reason.'); return;
        }
        const assessmentToReject = selectedAssessmentForRejection;

        // --- Updated Validation ---
        // Check if it's already been rejected by this lender according to our filtered data
        if (assessmentToReject.isRejectedByThisLender) {
            alert('You have already rejected this application.');
            return;
        }
        // Check other conditions like KYC status, accepted offers, etc.
        const kycIsApproved = String(getNested(assessmentToReject, 'kycStatus', '')).toUpperCase() === 'APPROVED';
        if (!kycIsApproved || assessmentToReject.hasAcceptedOffer || assessmentToReject.thisLenderOfferDetails) {
            let reason = "Cannot reject application: ";
            if (!kycIsApproved) reason += "KYC not approved. ";
            if (assessmentToReject.hasAcceptedOffer) reason += "An offer is already accepted. ";
            if (assessmentToReject.thisLenderOfferDetails) reason += "You already submitted an offer. ";
            alert(reason.trim());
            return;
        }
        // --- End Updated Validation ---

        setIsSubmittingRejection(true);
        const lenderId = localStorage.getItem('userId');
        const userId = assessmentToReject.userId;
        const creditLineIdToUpdate = assessmentToReject._id;

        if (!lenderId || !userId || !creditLineIdToUpdate) {
            alert('Missing required information for rejection.'); setIsSubmittingRejection(false); return;
        }

        // --- MODIFIED PAYLOAD ---
        // Payload now ONLY includes the ID and the new reviewHistory entry.
        // The backend MUST handle adding this entry to the array ($push in Mongoose)
        // without changing the main creditLineStatus.
        const payload = {
            userId: userId, // Usually needed for authorization/context on backend
            creditLineData: {
                _id: creditLineIdToUpdate, // Identify the document
                reviewHistory: [{          // The single new entry to add
                    reviewedBy: lenderId,
                    status: 'REJECTED',
                    notes: `Rejected by lender: ${rejectionReason}`, // Optional: add reason to notes
                    rejectionReasons: [rejectionReason],          // Store reason in dedicated field
                    reviewDate: new Date().toISOString()          // Timestamp the rejection action
                }]
                // DO NOT INCLUDE 'creditLineStatus' HERE
            },
            // reviewedBy: lenderId // May be required at top level by your specific API implementation
        };
        console.log("Rejection Payload (Adding Review Entry Only):", JSON.stringify(payload, null, 2));
        // --- END MODIFIED PAYLOAD ---

        try {
            // IMPORTANT: Ensure this backend endpoint correctly ADDS to the reviewHistory array
            // and does NOT change the main creditLineStatus.
            const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/creditLineCreateOrUpdate`, payload);
            if (!response.data?.success) {
                throw new Error(response.data?.message || "Backend reported failure during rejection update.");
            }
            alert("Application rejection recorded successfully.");
            setShowRejectionModal(false);
            await fetchData(); // Refresh data to update tab contents

        } catch (error) {
            console.error('Error submitting rejection:', error.response?.data || error.message);
            alert(`Error rejecting application: ${getNested(error, 'response.data.message', error.message) || 'An unknown error occurred'}`);
        } finally {
            setIsSubmittingRejection(false);
        }
    }, [selectedAssessmentForRejection, rejectionReason, fetchData]); // Dependencies include the selected assessment and reason

    const flattenCreditAssessmentData = (assessment, maxShareholders, maxDirectors, maxBuyers, getNestedFn, formatDateFn, addDocFn) => {
        const rowData = {};
        const user = assessment.userDetails || {}; // Fallback to empty object if userDetails is missing

        // ---- I. Assessment & Credit Line Level Info ----
        rowData['Assessment ID (CL ID)'] = getNestedFn(assessment, '_id', 'N/A');
        rowData['Application Date'] = formatDateFn(getNestedFn(assessment, 'createdAt'));
        rowData['Application Age'] = calculateAge(getNestedFn(assessment, 'createdAt')); // Uses your existing calculateAge
        rowData['Credit Line Status (Overall)'] = getNestedFn(assessment, 'creditLineStatus', 'N/A');
        rowData['Display Status on Page'] = getNestedFn(assessment, 'displayStatus', 'N/A');
        rowData['User ID (Borrower)'] = getNestedFn(assessment, 'userId', 'N/A');
        rowData['Requested Credit Limit'] = getNestedFn(assessment, 'requestedCreditLimit', 'N/A');
        rowData['Requested Tenure Days'] = getNestedFn(assessment, 'requestedTenureDays', 'N/A');
        rowData['Purpose'] = getNestedFn(assessment, 'purpose', 'N/A');
        rowData['Credit Score (if available)'] = getNestedFn(assessment, 'creditScore', 'N/A');
        rowData['Risk Rating (if available)'] = getNestedFn(assessment, 'riskRating', 'N/A');
        rowData['Last Review Date'] = formatDateFn(getNestedFn(assessment, 'lastReviewDate'));
        rowData['Next Review Date'] = formatDateFn(getNestedFn(assessment, 'nextReviewDate'));

        // ---- II. User/Borrower Basic Info (from assessment.userDetails) ----
        rowData['User First Name'] = getNestedFn(user, 'firstName', 'N/A');
        rowData['User Middle Name'] = getNestedFn(user, 'middleName', 'N/A');
        rowData['User Last Name'] = getNestedFn(user, 'lastName', 'N/A');
        rowData['User Email'] = getNestedFn(user, 'email', 'N/A');
        rowData['User Mobile No'] = getNestedFn(user, 'mobileNo', 'N/A');
        rowData['User Account Active'] = getNestedFn(user, 'isActive', 'N/A');

        // ---- III. KYC Details (from assessment.userDetails.kyc and modal structure) ----
        rowData['Overall KYC Status'] = getNestedFn(assessment, 'kycStatus', 'N/A'); // This is on assessment object
        rowData['KYC Verified/Processed On'] = formatDateFn(getNestedFn(user, 'kyc.verifiedOn'));
        rowData['KYC Overall Notes'] = getNestedFn(user, 'kyc.verificationNotes', '');

        // KYC - Personal Address
        rowData['KYC Personal Address Line 1'] = getNestedFn(user, 'kyc.addressLine1', 'N/A');
        rowData['KYC Personal Address Line 2'] = getNestedFn(user, 'kyc.addressLine2', 'N/A');
        rowData['KYC Personal City'] = getNestedFn(user, 'kyc.city', 'N/A');
        rowData['KYC Personal State'] = getNestedFn(user, 'kyc.state', 'N/A');
        rowData['KYC Personal Postal Code'] = getNestedFn(user, 'kyc.postalCode', 'N/A');
        rowData['KYC Personal Country'] = getNestedFn(user, 'kyc.country', 'N/A');

        // KYC - Income/Bank Details
        rowData['KYC Bank Account Number'] = getNestedFn(user, 'kyc.incomeDetails.accountNumber', 'N/A');
        rowData['KYC Bank IBAN/IFSC'] = getNestedFn(user, 'kyc.incomeDetails.ifscCode', 'N/A');
        // Add other income details fields if present in modal, e.g., source of wealth

        // KYC - Employment Details
        rowData['KYC Employer Name'] = getNestedFn(user, 'kyc.employmentDetails.employerName', 'N/A');
        rowData['KYC Employment Position'] = getNestedFn(user, 'kyc.employmentDetails.position', 'N/A');

        // Business Details (from user.kyc.businessDetails)
        rowData['Business Name'] = getNestedFn(user, 'kyc.businessDetails.businessName', 'N/A');
        rowData['Legal Entity Name'] = getNestedFn(user, 'kyc.businessDetails.legalEntityName', 'N/A');
        rowData['Establishment Name'] = getNestedFn(user, 'kyc.businessDetails.establishmentName', 'N/A');
        rowData['Legal Form'] = getNestedFn(user, 'kyc.businessDetails.legalForm', 'N/A');
        rowData['Ownership Type'] = getNestedFn(user, 'kyc.businessDetails.ownershipType', 'N/A');
        rowData['Sector'] = getNestedFn(user, 'kyc.businessDetails.sector', 'N/A');
        rowData['Firm Nationality'] = getNestedFn(user, 'kyc.businessDetails.firmNationality', 'N/A');
        rowData['CR Number'] = getNestedFn(user, 'kyc.businessDetails.crNumber', 'N/A');
        rowData['CR Issue Date'] = formatDateFn(getNestedFn(user, 'kyc.businessDetails.crIssueDate'));
        rowData['CR Expiry Date'] = formatDateFn(getNestedFn(user, 'kyc.businessDetails.crExpiryDate'));
        rowData['Trade License (TL) Number'] = getNestedFn(user, 'licenseNumber', 'N/A'); // From root user as per modal
        rowData['TL Issue Date'] = formatDateFn(getNestedFn(user, 'kyc.businessDetails.tlIssueDate'));
        rowData['TL Expiry Date'] = formatDateFn(getNestedFn(user, 'kyc.businessDetails.tlExpiryDate'));
        rowData['Tax Reg No (TRN)'] = getNestedFn(user, 'kyc.businessDetails.taxRegNo', 'N/A');
        rowData['TIN Number'] = getNestedFn(user, 'kyc.businessDetails.tinNumber', 'N/A');
        rowData['Establishment ID'] = getNestedFn(user, 'kyc.businessDetails.establishmentId', 'N/A');
        rowData['Establishment ID Issue Date'] = formatDateFn(getNestedFn(user, 'kyc.businessDetails.establishmentIdIssueDate'));
        rowData['Establishment ID Expiry Date'] = formatDateFn(getNestedFn(user, 'kyc.businessDetails.establishmentIdExpiryDate'));
        rowData['Branch Count'] = getNestedFn(user, 'kyc.businessDetails.branchCount', 'N/A');
        rowData['Business Address Line 1'] = getNestedFn(user, 'kyc.businessDetails.businessAddressLine1', 'N/A');
        rowData['Business Address Line 2'] = getNestedFn(user, 'kyc.businessDetails.businessAddressLine2', 'N/A');
        rowData['Business City'] = getNestedFn(user, 'kyc.businessDetails.businessCity', 'N/A');
        rowData['Business Country'] = getNestedFn(user, 'kyc.businessDetails.businessCountry', 'N/A');

        // Root Business Documents (from user object based on KycDetailsViewModal's rootBusinessDocFields)
        const rootBusinessDocFields = [
            { key: 'commercialRegistration', label: 'CR' }, { key: 'tradeLicense', label: 'Trade License' },
            { key: 'taxCard', label: 'Tax Card' }, { key: 'establishmentCard', label: 'Establishment Card' },
            { key: 'memorandumOfAssociation', label: 'MOA' }, { key: 'articleOfAssociation', label: 'AOA' },
            { key: 'otherDocument', label: 'Other Doc 1' }, { key: 'otherDocumentTwo', label: 'Other Doc 2' },
            { key: 'otherDocument3', label: 'Other Doc 3' }, { key: 'otherDocument4', label: 'Other Doc 4' },
            { key: 'otherDocument5', label: 'Other Doc 5' }, { key: 'otherDocument6', label: 'Other Doc 6' },
            { key: 'otherDocument7', label: 'Other Doc 7' }, { key: 'otherDocument8', label: 'Other Doc 8' },
            { key: 'otherDocument9', label: 'Other Doc 9' }, { key: 'otherDocument10', label: 'Other Doc 10' },
        ];
        rootBusinessDocFields.forEach(field => {
            addDocFn(rowData, getNestedFn(user, field.key), `Business Doc - ${field.label}`, getNestedFn, formatDateFn);
        });

        // Root Financial Documents (from user object)
        const rootFinancialDocFields = [
            { key: 'bankStatement', label: 'Bank Statements' }, { key: 'auditedFinancialReport', label: 'Audited Financial Report' },
            { key: 'commercialCreditReport', label: 'CCR' }, { key: 'cashFlowLedger', label: 'Cash Flow Ledger' },
            { key: 'cashFlowDocument', label: 'Cash Flow Statement Doc' },
        ];
        rootFinancialDocFields.forEach(field => {
            addDocFn(rowData, getNestedFn(user, field.key), `Financial Doc - ${field.label}`, getNestedFn, formatDateFn);
        });

        // CAM File from userDetails
        addDocFn(rowData, getNestedFn(user, 'camFile'), 'CAM Report', getNestedFn, formatDateFn);


        // Shareholders (from user.shareholders)
        const shareholders = getNestedFn(user, 'shareholders', []) || [];
        for (let i = 0; i < maxShareholders; i++) {
            const sh = shareholders[i];
            const prefix = `Shareholder ${i + 1}`;
            if (sh) {
                rowData[`${prefix} - First Name`] = getNestedFn(sh, 'firstName', 'N/A');
                rowData[`${prefix} - Last Name`] = getNestedFn(sh, 'lastName', 'N/A');
                rowData[`${prefix} - Email`] = getNestedFn(sh, 'email', 'N/A');
                rowData[`${prefix} - KYC Status`] = getNestedFn(sh, 'kycVerificationStatus', 'N/A');
                rowData[`${prefix} - Address Zone`] = getNestedFn(sh, 'address.zone', 'N/A');
                rowData[`${prefix} - Address Street`] = getNestedFn(sh, 'address.streetNo', 'N/A');
                rowData[`${prefix} - Address Building`] = getNestedFn(sh, 'address.buildingNo', 'N/A');
                // Add more shareholder fields displayed in modal if any (e.g. AML, Video KYC if applicable here)
                addDocFn(rowData, getNestedFn(sh, 'passport'), `${prefix} - Passport`, getNestedFn, formatDateFn);
                addDocFn(rowData, getNestedFn(sh, 'qid'), `${prefix} - QID`, getNestedFn, formatDateFn);
                addDocFn(rowData, getNestedFn(sh, 'proofOfAddress'), `${prefix} - Proof of Address`, getNestedFn, formatDateFn);
            } else {
                ['First Name', 'Last Name', 'Email', 'KYC Status', 'Address Zone', 'Address Street', 'Address Building'].forEach(f => rowData[`${prefix} - ${f}`] = '');
                ['Passport', 'QID', 'Proof of Address'].forEach(docType => {
                    ['File Name', 'Uploaded On', 'Status', 'Notes', 'Verification Date', 'Signed URL', 'MIME Type'].forEach(field => rowData[`${prefix} - ${docType} - ${field}`] = '');
                });
            }
        }

        // Directors (from user.kyc.directors) - Excluding Auth Signatories / Beneficial Owners per previous preference
        const directors = getNestedFn(user, 'kyc.directors', []) || [];
        for (let i = 0; i < maxDirectors; i++) {
            const dir = directors[i];
            const prefix = `Director ${i + 1}`;
            if (dir) {
                rowData[`${prefix} - Name`] = getNestedFn(dir, 'directorName', 'N/A');
                rowData[`${prefix} - Position`] = getNestedFn(dir, 'position', 'N/A');
                rowData[`${prefix} - Nationality`] = getNestedFn(dir, 'nationality', 'N/A');
                rowData[`${prefix} - DOB`] = formatDateFn(getNestedFn(dir, 'dateOfBirth'));
                rowData[`${prefix} - National ID`] = getNestedFn(dir, 'nationalId', 'N/A');
                rowData[`${prefix} - Address`] = getNestedFn(dir, 'directorAddress', 'N/A');
                // Add UBO, Signatory flags if present on director object and shown in modal
                // addDocFn(rowData, getNestedFn(dir, 'idDocument'), `${prefix} - ID Document`, getNestedFn, formatDateFn); // If directors have separate docs
            } else {
                ['Name', 'Position', 'Nationality', 'DOB', 'National ID', 'Address'].forEach(f => rowData[`${prefix} - ${f}`] = '');
                // ['ID Document'].forEach(docType => { ... blank doc fields ... });
            }
        }

        // Top Buyers (from user.kyc.buyers)
        const buyers = getNestedFn(user, 'kyc.buyers', []) || [];
        for (let i = 0; i < maxBuyers; i++) {
            const buyer = buyers[i];
            const prefix = `Buyer ${i + 1}`;
            if (buyer) {
                rowData[`${prefix} - Name`] = getNestedFn(buyer, 'buyerName', 'N/A');
                rowData[`${prefix} - Contact Person`] = getNestedFn(buyer, 'contactPerson', 'N/A');
                rowData[`${prefix} - Contact Phone`] = getNestedFn(buyer, 'contactPhone', 'N/A');
                rowData[`${prefix} - Contact Email`] = getNestedFn(buyer, 'contactEmail', 'N/A');
                rowData[`${prefix} - Reg No`] = getNestedFn(buyer, 'registrationNumber', 'N/A');
                // addDocFn(rowData, getNestedFn(buyer, 'companyDocument'), `${prefix} - Company Doc`, getNestedFn, formatDateFn); // If buyers have docs
            } else {
                ['Name', 'Contact Person', 'Contact Phone', 'Contact Email', 'Reg No'].forEach(f => rowData[`${prefix} - ${f}`] = '');
                // ['Company Doc'].forEach(docType => { ... blank doc fields ... });
            }
        }

        // ---- IV. Offer Details (if any, from assessment.acceptedOrActiveOfferDetails or .pendingOfferDetails) ----
        const offer = assessment.acceptedOrActiveOfferDetails || assessment.pendingOfferDetails;
        if (offer) {
            rowData['Offer ID'] = getNestedFn(offer, '_id', 'N/A');
            rowData['Offer Status'] = getNestedFn(offer, 'status', 'N/A');
            rowData['Offer Credit Limit (QAR)'] = getNestedFn(offer, 'creditLimit', 'N/A');
            rowData['Offer Tenure (Days)'] = getNestedFn(offer, 'tenureDays', 'N/A');
            rowData['Offer Interest Rate (APR %)'] = getNestedFn(offer, 'interestRate', 'N/A');
            rowData['Offer Processing Fee Type'] = getNestedFn(offer, 'processingFee.type', 'N/A');
            rowData['Offer Processing Fee Value'] = getNestedFn(offer, 'processingFee.value', 'N/A');
            const calculatedFee = calculateCurrentFee(getNestedFn(offer, 'processingFee.type'), getNestedFn(offer, 'processingFee.value'), getNestedFn(offer, 'creditLimit'));
            rowData['Offer Calculated Processing Fee (QAR)'] = isNaN(calculatedFee) ? 'N/A' : calculatedFee.toFixed(2);
            rowData['Offer Risk Profile'] = getNestedFn(offer, 'riskProfile', 'N/A');
            rowData['Offer Currency'] = getNestedFn(offer, 'currency', 'N/A');
            rowData['Offer Internal Notes'] = getNestedFn(offer, 'notes', '');
            rowData['Offer Created At'] = formatDateFn(getNestedFn(offer, 'createdAt'));
            rowData['Offer Updated At'] = formatDateFn(getNestedFn(offer, 'updatedAt'));
            rowData['Offer Expiry Date'] = formatDateFn(getNestedFn(offer, 'expiryDate'));
            rowData['Offer Accepted Date'] = formatDateFn(getNestedFn(offer, 'acceptedDate'));
            rowData['Offer Contract Accepted Date'] = formatDateFn(getNestedFn(offer, 'loanContractAcceptedDate'));
            rowData['Offer Lender ID'] = getNestedFn(offer, 'lenderId', 'N/A');
        } else {
            ['Offer ID', 'Offer Status', 'Offer Credit Limit (QAR)', 'Offer Tenure (Days)', 'Offer Interest Rate (APR %)',
                'Offer Processing Fee Type', 'Offer Processing Fee Value', 'Offer Calculated Processing Fee (QAR)',
                'Offer Risk Profile', 'Offer Currency', 'Offer Internal Notes', 'Offer Created At', 'Offer Updated At', 'Offer Expiry Date',
                'Offer Accepted Date', 'Offer Contract Accepted Date', 'Offer Lender ID'
            ].forEach(f => rowData[f] = 'N/A');
        }

        // ---- V. Rejection Details (if any, from assessment.rejectionDetails) ----
        const rejection = assessment.rejectionDetails;
        if (rejection) {
            rowData['Rejection Date'] = formatDateFn(getNestedFn(rejection, 'date'));
            rowData['Rejection Reason'] = getNestedFn(rejection, 'reason', 'N/A');
            rowData['Rejected By (Lender ID)'] = getNestedFn(rejection, 'rejectedBy', 'N/A');
        } else {
            ['Rejection Date', 'Rejection Reason', 'Rejected By (Lender ID)'].forEach(f => rowData[f] = 'N/A');
        }

        // ---- VI. Review History (from assessment.reviewHistory - potentially summarize or take latest) ----
        const reviewHistory = getNestedFn(assessment, 'reviewHistory', []) || [];
        if (reviewHistory.length > 0) {
            const latestReview = reviewHistory[reviewHistory.length - 1]; // Get the last review entry
            rowData['Latest Review Status'] = getNestedFn(latestReview, 'status', 'N/A');
            rowData['Latest Review Date'] = formatDateFn(getNestedFn(latestReview, 'reviewDate'));
            rowData['Latest Review Notes'] = getNestedFn(latestReview, 'notes', '');
            rowData['Latest Reviewed By (ID)'] = getNestedFn(latestReview, 'reviewedBy', 'N/A');
            rowData['Latest Rejection Reasons'] = (getNestedFn(latestReview, 'rejectionReasons', []) || []).join(', ');
        } else {
            ['Latest Review Status', 'Latest Review Date', 'Latest Review Notes', 'Latest Reviewed By (ID)', 'Latest Rejection Reasons'].forEach(f => rowData[f] = 'N/A');
        }

        return rowData;
    };

    const handleExportAssessments = async () => {
        let itemsToExport = [];
        let sheetName = "Assessments_Export";

        if (activeTopLevelTab === 'applications') {
            itemsToExport = filteredApplicationAssessments;
            sheetName = "Applications_Under_Review";
        } else if (activeTopLevelTab === 'creditOffers') {
            itemsToExport = filteredOfferAssessments;
            sheetName = "Credit_Offers_Made";
        } else if (activeTopLevelTab === 'rejected') {
            itemsToExport = filteredRejectedAssessments;
            sheetName = "Rejected_Cases";
        }

        if (!itemsToExport || itemsToExport.length === 0) {
            alert("No data in the current view to export.");
            return;
        }
        setIsExportingExcel(true);

        try {
            let maxShareholders = 0;
            let maxDirectors = 0;
            let maxBuyers = 0;

            itemsToExport.forEach(assessment => {
                const user = assessment.userDetails || {};
                const shareholders = getNested(user, 'shareholders', []) || [];
                if (shareholders.length > maxShareholders) maxShareholders = shareholders.length;

                const directors = getNested(user, 'kyc.directors', []) || [];
                if (directors.length > maxDirectors) maxDirectors = directors.length;

                const buyers = getNested(user, 'kyc.buyers', []) || [];
                if (buyers.length > maxBuyers) maxBuyers = buyers.length;
            });

            if (maxShareholders === 0) maxShareholders = 1;
            if (maxDirectors === 0) maxDirectors = 1;
            if (maxBuyers === 0) maxBuyers = 1;

            const excelData = itemsToExport.map(assessment =>
                flattenCreditAssessmentData(assessment, maxShareholders, maxDirectors, maxBuyers, getNested, formatDate, addDocumentFieldsToRow)
            );

            if (excelData.length === 0) {
                alert("No data to export after processing.");
                setIsExportingExcel(false);
                return;
            }

            const worksheet = XLSX.utils.json_to_sheet(excelData);

            if (excelData.length > 0 && excelData[0]) {
                const headers = Object.keys(excelData[0]);
                const colWidths = headers.map(header => {
                    const headerLength = header ? header.toString().length : 10;
                    return { wch: headerLength + 2 }; // Padding of +2
                });
                worksheet['!cols'] = colWidths;
            }

            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

            const now = new Date();
            const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`; XLSX.writeFile(workbook, `Credit_Assessments_${sheetName}_${timestamp}.xlsx`);

        } catch (error) {
            console.error("Error exporting assessment data to Excel:", error);
            alert("An error occurred while exporting assessment data. Please check the console.");
        } finally {
            setIsExportingExcel(false);
        }
    };

    // --- Render Offer Form Section (Uses Tailwind) ---
    const renderOfferFormSection = () => {
        if (!selectedAssessmentForOffer) return null;
        const assessment = selectedAssessmentForOffer;
        const userDetails = assessment.userDetails;
        const kycApproved = String(getNested(assessment, 'kycStatus', '')).toUpperCase() === 'APPROVED';
        const offerAlreadyAccepted = assessment.hasAcceptedOffer;
        const offerAlreadySubmitted = assessment.thisLenderOfferDetails;
        // Determine if the form should be submittable
        const canSubmit = kycApproved && !offerAlreadyAccepted && !offerAlreadySubmitted;
        return (
            <div className="bg-white p-8 rounded-lg">
                <h3 className="text-2xl font-bold text-gray-800 mb-3">
                    Submit New Credit Line Offer
                </h3>
                <div className="mb-6 border-b border-gray-200 pb-5">
                    <p className="text-sm text-gray-700">
                        Creating offer for <span className="font-semibold">{getNested(userDetails, 'firstName')} {getNested(userDetails, 'lastName')}</span>
                    </p>
                    <div className="mt-2 flex items-center">
                        <span className="text-sm mr-2">KYC Status:</span>
                        <span className={`px-3 py-1 text-xs font-medium rounded-full ${kycApproved ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {getNested(assessment, 'kycStatus', 'N/A')}
                        </span>
                    </div>
                    {offerAlreadyAccepted &&
                        <div className="mt-3 p-3 bg-red-50 border-l-4 border-red-500 rounded-md">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <div className="ml-3">
                                    <p className="text-sm text-red-700">An offer has already been accepted by the borrower.</p>
                                </div>
                            </div>
                        </div>
                    }
                    {offerAlreadySubmitted &&
                        <div className="mt-3 p-3 bg-yellow-50 border-l-4 border-yellow-500 rounded-md">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <div className="ml-3">
                                    <p className="text-sm text-yellow-700">You have already submitted an offer for this application.</p>
                                </div>
                            </div>
                        </div>
                    }
                </div>
                <form onSubmit={(e) => { e.preventDefault(); if (canSubmit) { handleSubmitOffer(); } }}>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                        {/* Credit Limit */}
                        <div className="relative">
                            <label htmlFor="offerCreditLimit" className="block text-sm font-medium text-gray-700 mb-1">
                                Credit Limit (QAR) <span className="text-red-500">*</span>
                            </label>
                            <div className="relative mt-1 rounded-md shadow-sm">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 sm:text-sm">QAR</span>
                                </div>
                                <input
                                    type="number" id="offerCreditLimit" name="creditLimit" min="1" step="any"
                                    value={offerData.creditLimit}
                                    onChange={(e) => setOfferData({ ...offerData, creditLimit: e.target.value })}
                                    className="pl-14 py-2.5 block w-full rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500"
                                    placeholder="50,000" required disabled={!canSubmit || isSubmittingOffer}
                                />
                            </div>
                        </div>
                        {/* Tenure */}
                        <div>
                            <label htmlFor="offerTenure" className="block text-sm font-medium text-gray-700 mb-1">
                                Tenure (Days) <span className="text-red-500">*</span>
                            </label>
                            <div className="relative mt-1 rounded-md shadow-sm">
                                <input
                                    type="number" id="offerTenure" name="tenureDays" min="1" step="1"
                                    value={offerData.tenureDays}
                                    onChange={(e) => setOfferData({ ...offerData, tenureDays: e.target.value })}
                                    className="py-2.5 px-3 block w-full rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500"
                                    placeholder="90" required disabled={!canSubmit || isSubmittingOffer}
                                />
                                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 sm:text-sm">days</span>
                                </div>
                            </div>
                        </div>
                        {/* Interest Rate */}
                        <div>
                            <label htmlFor="offerInterestRate" className="block text-sm font-medium text-gray-700 mb-1">
                                Interest Rate (APR %) <span className="text-red-500">*</span>
                            </label>
                            <div className="relative mt-1 rounded-md shadow-sm">
                                <input
                                    type="number" id="offerInterestRate" name="interestRate" min="0" step="0.01"
                                    value={offerData.interestRate}
                                    onChange={(e) => setOfferData({ ...offerData, interestRate: e.target.value })}
                                    className="py-2.5 px-3 block w-full rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500"
                                    placeholder="12.5" required disabled={!canSubmit || isSubmittingOffer}
                                />
                                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 sm:text-sm">%</span>
                                </div>
                            </div>
                        </div>
                        {/* Processing Fee */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Processing Fee <span className="text-red-500">*</span>
                            </label>
                            <div className="flex items-center space-x-2">
                                <div className="relative flex-grow">
                                    <input
                                        type="number" name="processingFeeValue" min="0"
                                        step={offerData.processingFeeType === 'percentage' ? '0.01' : '1'}
                                        value={offerData.processingFeeValue}
                                        onChange={(e) => setOfferData({ ...offerData, processingFeeValue: e.target.value })}
                                        className="py-2.5 px-3 block w-full rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500"
                                        placeholder="Fee value" aria-label="Processing Fee Value" required disabled={!canSubmit || isSubmittingOffer}
                                    />
                                </div>
                                <select
                                    name="processingFeeType" value={offerData.processingFeeType}
                                    onChange={(e) => setOfferData({ ...offerData, processingFeeType: e.target.value })}
                                    className="py-2.5 px-3 block w-24 rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500"
                                    aria-label="Processing Fee Type" disabled={!canSubmit || isSubmittingOffer}
                                >
                                    <option value="flat">QAR</option>
                                    <option value="percentage">%</option>
                                </select>
                            </div>
                            {/* Fee Calculation Display */}
                            {(() => {
                                const currentFee = calculateCurrentFee(offerData.processingFeeType, offerData.processingFeeValue, offerData.creditLimit);
                                const maxAllowed = calculateMaxAllowedFee(offerData.creditLimit);
                                const isExceeded = currentFee > maxAllowed && offerData.creditLimit > 0;
                                return (<>
                                    {/* <p className="mt-2 text-xs text-gray-500">Max: {MAX_PROCESSING_FEE_PERCENTAGE}% or QAR {MAX_PROCESSING_FEE_FLAT}. Current Max: QAR {maxAllowed.toFixed(2)}</p> */}
                                    <p className={`mt-1 text-xs ${isExceeded ? 'text-red-600 font-semibold' : 'text-emerald-600'}`}>
                                        Calculated Fee: QAR {currentFee.toFixed(2)} {isExceeded ?
                                            <span className="inline-flex items-center ml-1">
                                                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path></svg>
                                                Exceeds Max!
                                            </span> : ''}
                                    </p>
                                </>);
                            })()}
                        </div>
                        {/* Risk Profile */}
                        {/* <div>
                            <label htmlFor="offerRiskProfile" className="block text-sm font-medium text-gray-700 mb-1">Risk Profile</label>
                            <select
                                id="offerRiskProfile" name="riskProfile" value={offerData.riskProfile}
                                onChange={(e) => setOfferData({ ...offerData, riskProfile: e.target.value })}
                                className="py-2.5 px-3 block w-full rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500"
                                disabled={!canSubmit || isSubmittingOffer}
                            >
                                <option value="LOW">Low</option>
                                <option value="MEDIUM">Medium</option>
                                <option value="HIGH">High</option>
                            </select>
                        </div> */}
                        {/* Notes */}
                        <div className="md:col-span-2">
                            <label htmlFor="offerNotes" className="block text-sm font-medium text-gray-700 mb-1">Internal Notes (Optional)</label>
                            <textarea
                                id="offerNotes" name="notes" value={offerData.notes}
                                onChange={(e) => setOfferData({ ...offerData, notes: e.target.value })}
                                className="py-2.5 px-3 block w-full rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-50 disabled:text-gray-500"
                                rows="3" placeholder="Add internal notes about this offer..."
                                disabled={!canSubmit || isSubmittingOffer}
                            ></textarea>
                        </div>
                        {/* Submit Button Area */}
                        <div className="md:col-span-2 mt-6 pt-4 border-t border-gray-200 flex justify-end">
                            {(() => {
                                const requiredMissing = !offerData.creditLimit || !offerData.tenureDays || offerData.interestRate === '' || offerData.processingFeeValue === '';
                                const feeExceeded = calculateCurrentFee(offerData.processingFeeType, offerData.processingFeeValue, offerData.creditLimit) > calculateMaxAllowedFee(offerData.creditLimit) && offerData.creditLimit > 0;
                                const isDisabled = !canSubmit || isSubmittingOffer || requiredMissing || feeExceeded;
                                return (
                                    <button
                                        type="submit"
                                        disabled={isDisabled}
                                        className={`inline-flex justify-center items-center py-2.5 px-6 border border-transparent text-sm font-medium rounded-md text-white
                                        ${isDisabled ? 'bg-gray-400' : 'bg-indigo-600 hover:bg-indigo-700 shadow-md hover:shadow-lg transition-all duration-150'}
                                        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-60 disabled:cursor-not-allowed`}
                                    >
                                        {isSubmittingOffer ? (
                                            <>
                                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                Submitting...
                                            </>
                                        ) : 'Submit Offer'}
                                    </button>
                                );
                            })()}
                        </div>
                    </div>
                </form>
            </div>
        );
    };

    // --- KYC Status Badge Component ---
    // Using the shared StatusBadge component
    const KycStatusBadge = ({ status }) => {
        return <StatusBadge status={String(status || 'N/A').toUpperCase()} />;
    };

    // --- Offer/Marketplace Status Badge Component ---
    // Using the shared StatusBadge component
    const MarketplaceStatusBadge = ({ status }) => {
        return <StatusBadge status={String(status || 'Unknown').toUpperCase()} />;
    };


    // --- MAIN COMPONENT RENDER ---
    return (
        <div className="p-4 md:p-6 bg-gray-100 min-h-screen">
            <div className="flex justify-between items-center mb-6"> {/* Flex container for title and button */}
                <h1 className="text-2xl font-bold text-gray-800">Credit Applications</h1>
                {/* Determine which list of assessments is currently relevant for export count */}
                {((activeTopLevelTab === 'applications' && filteredApplicationAssessments.length > 0) ||
                    (activeTopLevelTab === 'creditOffers' && filteredOfferAssessments.length > 0) ||
                    (activeTopLevelTab === 'rejected' && filteredRejectedAssessments.length > 0)) && (
                        <button
                            onClick={handleExportAssessments} // We will define this function
                            disabled={isExportingExcel}
                            className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 ${isExportingExcel ? 'opacity-50 cursor-not-allowed' : ''
                                }`}
                        >
                            {isExportingExcel ? (
                                <>
                                    {/* SVG Spinner */}
                                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Exporting...
                                </>
                            ) : (
                                <>
                                    {/* Download Icon SVG */}
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    Export to Excel
                                </>
                            )}
                        </button>
                    )}
            </div>
            <AssessmentFilterSection
                filters={assessmentFilters}
                setFilters={setAssessmentFilters}
                resetFilters={resetAssessmentFilters}
            />

            {/* Top Level Tab Navigation */}
            <div className="mb-6 border-b border-gray-200">
                <nav className="-mb-px flex space-x-6 overflow-x-auto" aria-label="Main Tabs">
                    <button onClick={() => setActiveTopLevelTab('applications')}
                        className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTopLevelTab === 'applications'
                                ? 'border-indigo-500 text-indigo-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                        Applications ({loading ? '...' : applicationAssessments.length})
                    </button>
                    <button onClick={() => setActiveTopLevelTab('creditOffers')}
                        className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTopLevelTab === 'creditOffers'
                                ? 'border-indigo-500 text-indigo-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                        Credit Offers ({loading ? '...' : offerAssessments.length})
                    </button>
                    <button onClick={() => setActiveTopLevelTab('rejected')}
                        className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTopLevelTab === 'rejected'
                                ? 'border-indigo-500 text-indigo-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                        Rejected Cases ({loading ? '...' : rejectedAssessments.length})
                    </button>
                </nav>
            </div>

            {/* Loading & Error Display */}
            {loading && (
                <div className="text-center py-10 text-gray-500 italic">Loading assessment data... Please wait.</div>
            )}
            {fetchError && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{fetchError}</span>
                    <button onClick={fetchData} className="ml-4 py-1 px-2 border border-red-300 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200">
                        Retry Fetch
                    </button>
                </div>
            )}

            {/* Content Area */}
            {!loading && !fetchError && (
                <>
                    {/* Tab 1: Applications */}
                    {activeTopLevelTab === 'applications' && (
                        <div className="space-y-4">
                            <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded text-sm">
                                Review applications with APPROVED KYC status that require a credit line offer. Use KYC Details to view info,
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline align-text-bottom mx-1 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" /></svg>
                                to submit an offer, or
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline align-text-bottom mx-1 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
                                to reject.
                            </div>
                            <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Borrower</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Application Age</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KYC Status</th>
                                            <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Credit Report</th>
                                            <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                                            <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {filteredApplicationAssessments.length > 0 ? filteredApplicationAssessments.map((assessment) => {
                                            const kycStatus = getNested(assessment, 'kycStatus', 'N/A');
                                            // Action possible if KYC approved and no offer accepted or submitted by this lender
                                            // const canTakeAction = kycStatus === 'APPROVED' && !assessment.hasAcceptedOffer && !assessment.thisLenderOfferDetails;

                                            return (
                                                <tr key={assessment._id} className="hover:bg-gray-50">
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{getNested(assessment, 'userDetails.kyc.businessDetails.businessName', 'N/A')}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                                        <div className="font-medium">{[getNested(assessment, 'userDetails.firstName'), getNested(assessment, 'userDetails.lastName')].filter(Boolean).join(' ')}</div>
                                                        <div className="text-xs text-gray-500">{getNested(assessment, 'userDetails.email', 'N/A')}</div>
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatPhoneNumber(getNested(assessment, 'userDetails.mobileNo', 'N/A'))}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatDate(assessment.createdAt)}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{calculateAge(assessment.createdAt)}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                                                        <KycStatusBadge status={kycStatus} />
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                                        {assessment.userDetails?.camFile?.signedUrl ? (
                                                            <a
                                                                href={assessment.userDetails.camFile.signedUrl}
                                                                target="_blank"
                                                                rel="noopener noreferrer"
                                                                className="text-indigo-600 hover:text-indigo-800 text-xs break-all block underline"
                                                                title="View CAM Report"
                                                            >
                                                                View
                                                            </a>
                                                        ) : (
                                                            <div className="flex flex-col items-center">
                                                                <span className="text-xs text-gray-400 italic mb-1">N/A</span>
                                                            </div>
                                                        )}
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                                        <button
                                                            onClick={() => handleViewKycDetails(assessment)}
                                                            className="text-indigo-600 hover:text-indigo-900 disabled:text-gray-400 disabled:cursor-not-allowed"
                                                            title="View KYC Details"
                                                            disabled={!assessment.userDetails}
                                                        >
                                                            KYC Details
                                                        </button>
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                                        <span className="text-xs text-gray-400 italic">N/A</span>
                                                    </td>
                                                </tr>
                                            );
                                        }) : (
                                            <tr><td colSpan="8" className="text-center py-10 px-4 text-sm text-gray-500 italic">No applications currently require an offer.</td></tr>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}

                    {/* Tab 2: Credit Offers */}
                    {activeTopLevelTab === 'creditOffers' && (
                        <div className="space-y-4">
                            <div className="bg-blue-50 border border-blue-200 text-blue-800 px-4 py-3 rounded text-sm">
                                This section lists credit lines where you have submitted an offer, or where an offer (from any lender) has been accepted/activated. This is a read-only view.
                            </div>
                            <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Borrower</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer Limit (QAR)</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer APR (%)</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer Tenure (Days)</th>
                                            <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Credit Report</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            {/* No Action Column */}
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {filteredOfferAssessments.length > 0 ? filteredOfferAssessments.map((assessment) => {
                                            // Determine which offer's details to show: accepted/active one takes precedence
                                            const offerToShow = assessment.acceptedOrActiveOfferDetails || assessment.thisLenderOfferDetails;

                                            return (
                                                <tr key={assessment._id} className={`hover:bg-gray-50 ${assessment.hasAcceptedOffer ? 'bg-green-50' : ''}`}>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{getNested(assessment, 'userDetails.kyc.businessDetails.businessName', 'N/A')}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                                        <div className="font-medium">{[getNested(assessment, 'userDetails.firstName'), getNested(assessment, 'userDetails.lastName')].filter(Boolean).join(' ')}</div>
                                                        <div className="text-xs text-gray-500">{getNested(assessment, 'userDetails.email', 'N/A')}</div>
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatDate(assessment.createdAt)}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{calculateAge(assessment.createdAt)}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{getNested(offerToShow, 'creditLimit', 'N/A')}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{getNested(offerToShow, 'interestRate', 'N/A')}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{getNested(offerToShow, 'tenureDays', 'N/A')}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                                        {assessment.userDetails?.camFile?.signedUrl ? (
                                                            <a
                                                                href={assessment.userDetails.camFile.signedUrl}
                                                                target="_blank"
                                                                rel="noopener noreferrer"
                                                                className="text-indigo-600 hover:text-indigo-800 text-xs break-all block underline"
                                                                title="View CAM Report"
                                                            >
                                                                View
                                                            </a>
                                                        ) : (
                                                            <div className="flex flex-col items-center">
                                                                <span className="text-xs text-gray-400 italic mb-1">N/A</span>

                                                            </div>
                                                        )}
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                                                        <MarketplaceStatusBadge status={assessment.creditLineStatus} />
                                                    </td>
                                                </tr>
                                            );
                                        }) : (
                                            <tr><td colSpan="9" className="text-center py-10 px-4 text-sm text-gray-500 italic">No submitted or accepted credit offers found.</td></tr>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}

                    {/* Tab 3: Rejected Cases */}
                    {activeTopLevelTab === 'rejected' && (
                        <div className="space-y-4">
                            <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded text-sm">
                                This section lists applications that were rejected specifically by you. This is a read-only view.
                            </div>
                            <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Borrower</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rejected On</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rejection Reason</th>
                                            {/* No Action Column */}
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {filteredRejectedAssessments.length > 0 ? filteredRejectedAssessments.map((assessment) => (
                                            <tr key={assessment._id} className="bg-red-50 hover:bg-red-100"> {/* Subtle red background */}
                                                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{getNested(assessment, 'userDetails.kyc.businessDetails.businessName', 'N/A')}</td>
                                                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                                    <div className="font-medium">{[getNested(assessment, 'userDetails.firstName'), getNested(assessment, 'userDetails.lastName')].filter(Boolean).join(' ')}</div>
                                                    <div className="text-xs text-gray-500">{getNested(assessment, 'userDetails.email', 'N/A')}</div>
                                                </td>
                                                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatPhoneNumber(getNested(assessment, 'userDetails.mobileNo', 'N/A'))}</td>
                                                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatDate(assessment.createdAt)}</td>
                                                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatDate(getNested(assessment, 'rejectionDetails.date'))}</td>
                                                <td className="px-4 py-3 text-sm text-gray-500 max-w-xs break-words whitespace-normal">{getNested(assessment, 'rejectionDetails.reason', 'N/A')}</td>
                                            </tr>
                                        )) : (
                                            <tr><td colSpan="6" className="text-center py-10 px-4 text-sm text-gray-500 italic">No applications rejected by you found.</td></tr>
                                        )}
                                    </tbody>
                                </table>
                            </div> {/* End Shadow/Table Container */}
                        </div> // End space-y-4 for rejected tab
                    )} {/* End Rejected Cases Tab Content */}

                </> // End conditional rendering for !loading && !fetchError
            )} {/* End Loading/Error Check */}

            {/* --- MODALS --- */}

            {/* KYC Details View Modal */}
            {showKycDetailsModal && selectedUserForKycModal && (
                <KycDetailsViewModal
                    show={showKycDetailsModal}
                    user={selectedUserForKycModal}
                    onClose={() => { setShowKycDetailsModal(false); setSelectedUserForKycModal(null); }}
                    activeTabState={[activeKycModalTab, setActiveKycModalTab]} // Pass state and setter
                />
            )}

            {/* Submit Offer Modal */}
            {showOfferModal && selectedAssessmentForOffer && (
                <div className="fixed inset-0 bg-black bg-opacity-70 backdrop-blur-sm flex justify-center items-start z-50 p-4 pt-16 overflow-y-auto transition-opacity duration-300">
                    {/* Modal Container with Animation */}
                    <div className="bg-white rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col transform transition-all duration-300 ease-out animate-modal-slide-in">
                        {/* Modal Header */}
                        <div className="px-6 py-4 bg-white border-b border-gray-200 flex justify-between items-center sticky top-0 z-10 flex-shrink-0">
                            <h2 className="text-xl font-bold text-gray-800 flex items-center">
                                <svg className="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                Create Credit Line Offer
                            </h2>
                            <button
                                onClick={() => {
                                    setShowOfferModal(false);
                                    setSelectedAssessmentForOffer(null);
                                }}
                                className="text-gray-500 hover:text-gray-700 p-1.5 rounded-full hover:bg-gray-100 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-gray-200"
                                aria-label="Close Offer Modal"
                            >
                                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        {/* Modal Body - Render the Offer Form Section with subtle background */}
                        <div className="flex-grow overflow-y-auto bg-white">
                            {renderOfferFormSection()}
                        </div>

                        {/* Modal Footer */}
                        <div className="px-6 py-3 bg-white border-t border-gray-200 flex justify-end sticky bottom-0 flex-shrink-0 shadow-sm">
                            <button
                                onClick={() => {
                                    setShowOfferModal(false);
                                    setSelectedAssessmentForOffer(null);
                                }}
                                type="button"
                                className="inline-flex justify-center items-center py-2.5 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-150"
                            >
                                <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}


            {/* Reject Application Modal */}
            {showRejectionModal && selectedAssessmentForRejection && (
                <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
                    {/* Modal Container */}
                    <div className="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden">
                        {/* Modal Header */}
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h2 className="text-lg font-semibold text-gray-800">
                                Reject Credit Line Application
                            </h2>
                        </div>

                        {/* Modal Body */}
                        <div className="p-6 space-y-4">
                            <p className="text-sm text-gray-600">
                                Please provide a reason for rejecting the application for borrower: <span className="font-medium">{getNested(selectedAssessmentForRejection, 'userDetails.firstName')} {getNested(selectedAssessmentForRejection, 'userDetails.lastName')}</span>.
                            </p>
                            <div>
                                <label htmlFor="rejectionReason" className="block text-sm font-medium text-gray-700 mb-1">
                                    Rejection Reason <span className="text-red-500">*</span>
                                </label>
                                <textarea
                                    id="rejectionReason"
                                    rows="4"
                                    value={rejectionReason}
                                    onChange={(e) => setRejectionReason(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100"
                                    placeholder="Enter reason..."
                                    disabled={isSubmittingRejection}
                                ></textarea>
                                {rejectionReason.trim().length === 0 && <p className="text-xs text-red-500 mt-1">Rejection reason is required.</p>}
                            </div>
                        </div>

                        {/* Modal Footer */}
                        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                            <button
                                onClick={() => { setShowRejectionModal(false); setSelectedAssessmentForRejection(null); setRejectionReason(''); }}
                                type="button"
                                className="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                disabled={isSubmittingRejection}
                            >
                                Close
                            </button>
                            <button
                                onClick={handleSubmitRejection}
                                type="button"
                                className="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={!rejectionReason.trim() || isSubmittingRejection}
                            >
                                {isSubmittingRejection ? 'Rejecting...' : 'Reject Application'}
                            </button>
                        </div>
                    </div> {/* End Modal Container */}
                </div> // End Modal Backdrop
            )}

        </div> // End Main Page Container
    );
}