import React, { useState } from 'react';

export default function CreditRiskAssessment() {
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [editedInvoiceData, setEditedInvoiceData] = useState(null);
  const [showRiskDropdown, setShowRiskDropdown] = useState(false);
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');

  // Risk assessment statuses
  const riskStatuses = [
    "Pending Assessment",
    "Low Risk",
    "Medium Risk",
    "High Risk",
    "Rejected",
    "More Information Required"
  ];

  // Initial data with invoices to assess
  const [pendingAssessments, setPendingAssessments] = useState([
    {
      id: 1,
      company: "GLOBAL OILS FACTORY",
      type: "Invoice Discounting",
      invoiceNumber: '7295',
      amount: "QAR 87,000.00",
      madadScore: 82,
      riskScore: null,
      creditStatus: "Pending Assessment",
      invoiceDate: '2024-12-16',
      dueDate: '2025-01-15',
      description: 'Raw Material Supply',
      reference: 'PO-2024-001',
      supplier: 'GLOBAL OILS FACTORY',
      customer: 'BIG TRADERS',
      customerAddress: 'BIG TRADERS',
      billingAddress: 'N/A',
      gstin: '27AAPFU0939F1ZV',
      signedUrl: '/assets/invoice.pdf',
      companyHistory: {
        yearsInBusiness: 8,
        previousDeals: 12,
        repaymentHistory: "Excellent",
        averageDaysToPay: 32
      },
      customerHistory: {
        reliabilityScore: 78,
        yearsInBusiness: 5,
        previousTransactions: 8
      },
      financialMetrics: {
        currentRatio: 1.8,
        debtToEquity: 0.65,
        quickRatio: 1.2,
        returnOnAssets: '12%'
      },
      riskAssessmentNotes: '',
      assessmentHistory: []
    }
  ]);

  const RISK_STATUS_STYLES = {
    'Pending Assessment': 'bg-blue-100 text-blue-800',
    'Low Risk': 'bg-green-100 text-green-800',
    'Medium Risk': 'bg-yellow-100 text-yellow-800',
    'High Risk': 'bg-orange-100 text-orange-800',
    'Rejected': 'bg-red-100 text-red-800',
    'More Information Required': 'bg-purple-100 text-purple-800'
  };

  const getRiskColorDot = (status) => {
    const colors = {
      'Pending Assessment': 'bg-blue-500',
      'Low Risk': 'bg-green-500',
      'Medium Risk': 'bg-yellow-500',
      'High Risk': 'bg-orange-500',
      'Rejected': 'bg-red-500',
      'More Information Required': 'bg-purple-500'
    };
    return colors[status] || 'bg-gray-500';
  };

  const handleAssess = (invoice) => {
    setSelectedInvoice(invoice);
    setEditedInvoiceData({ ...invoice });
    setShowReviewModal(true);
  };

  const handleRiskSelect = (newRisk) => {
    if (selectedInvoice) {
      if (newRisk === "Rejected") {
        // Show reason modal for rejection
        setShowReasonModal(true);
        return;
      }

      updateRiskStatus(newRisk);
    }
  };

  const updateRiskStatus = (newRisk, reason = '') => {
    const timestamp = new Date().toISOString();
    const historyEntry = {
      date: timestamp,
      status: newRisk,
      reason: reason,
      analyst: "John Doe" // This would come from auth context in a real app
    };

    const updatedInvoice = {
      ...editedInvoiceData,
      creditStatus: newRisk,
      riskScore: calculateRiskScore(newRisk),
      riskAssessmentNotes: editedInvoiceData.riskAssessmentNotes,
      assessmentHistory: [...(editedInvoiceData.assessmentHistory || []), historyEntry]
    };

    const updatedAssessments = pendingAssessments.map(assessment =>
      assessment.id === selectedInvoice.id
        ? updatedInvoice
        : assessment
    );

    setPendingAssessments(updatedAssessments);
    setSelectedInvoice(updatedInvoice);
    setEditedInvoiceData(updatedInvoice);
    setShowRiskDropdown(false);
    setShowReasonModal(false);
    setRejectionReason('');
  };

  const calculateRiskScore = (riskStatus) => {
    switch (riskStatus) {
      case 'Low Risk': return Math.floor(Math.random() * 15) + 85; // 85-100
      case 'Medium Risk': return Math.floor(Math.random() * 20) + 65; // 65-85
      case 'High Risk': return Math.floor(Math.random() * 25) + 40; // 40-65
      case 'Rejected': return Math.floor(Math.random() * 40); // 0-40
      default: return null;
    }
  };

  const handleRiskScoreChange = (value) => {
    // Ensure it's between 0 and 100
    const score = Math.min(100, Math.max(0, value));

    // Determine the appropriate risk status based on score
    let status = 'Pending Assessment';
    if (score !== null) {
      if (score >= 85) status = 'Low Risk';
      else if (score >= 65) status = 'Medium Risk';
      else if (score >= 40) status = 'High Risk';
      else status = 'Rejected';
    }

    setEditedInvoiceData(prev => ({
      ...prev,
      riskScore: score,
      creditStatus: status
    }));
  };

  const handleToggleRiskDropdown = (e) => {
    e.stopPropagation();
    setShowRiskDropdown(!showRiskDropdown);
  };

  const handleConfirmRejection = () => {
    if (rejectionReason.trim().length > 0) {
      updateRiskStatus('Rejected', rejectionReason);
    }
  };

  // Handle field changes in invoice details
  // const handleInvoiceFieldChange = (field, value) => {
  //   setEditedInvoiceData(prevData => ({
  //     ...prevData,
  //     [field]: value
  //   }));
  // };

  // Handle changes in assessment notes
  const handleNotesChange = (value) => {
    setEditedInvoiceData(prevData => ({
      ...prevData,
      riskAssessmentNotes: value
    }));
  };

  // Save edited invoice data
  const handleSaveAssessment = () => {
    const updatedAssessments = pendingAssessments.map(assessment =>
      assessment.id === selectedInvoice.id
        ? { ...editedInvoiceData }
        : assessment
    );

    setPendingAssessments(updatedAssessments);
    setSelectedInvoice(editedInvoiceData);
    alert("Credit assessment saved successfully!");
  };

  // Close dropdown when clicking outside
  const handleClickOutside = () => {
    if (showRiskDropdown) {
      setShowRiskDropdown(false);
    }
  };

  return (
    <div className="p-6" onClick={handleClickOutside}>
      <h1 className="text-2xl font-bold mb-6">Credit Risk Assessment</h1>

      {/* Risk Status Legend */}
      <div className="flex flex-wrap gap-4 bg-white p-4 rounded-lg shadow-md mb-6">
        <h3 className="w-full text-sm font-medium mb-2">Risk Assessment Legend:</h3>
        {riskStatuses.map(status => (
          <div key={status} className="flex items-center space-x-2">
            <div className={`w-3 h-3 ${getRiskColorDot(status)} rounded-full`}></div>
            <span className="text-sm">{status}</span>
          </div>
        ))}
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Pending Credit Assessments</h2>
          <div className="space-x-2">
            <select className="border rounded-md p-2">
              <option>All Risk Levels</option>
              {riskStatuses.map(status => (
                <option key={status}>{status}</option>
              ))}
            </select>
          </div>
        </div>

        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="p-4 text-left">Company</th>
              <th className="p-4 text-left">Invoice Number</th>
              <th className="p-4 text-left">Due Date</th>
              <th className="p-4 text-left">Amount</th>
              <th className="p-4 text-left">Madad Score</th>
              <th className="p-4 text-left">Risk Score</th>
              <th className="p-4 text-left">Risk Status</th>
              <th className="p-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {pendingAssessments.map((item) => (
              <tr key={item.id} className="border-t">
                <td className="p-4">{item.company}</td>
                <td className="p-4">{item.invoiceNumber}</td>
                <td className="p-4">{item.dueDate}</td>
                <td className="p-4">{item.amount}</td>
                <td className="p-4">{item.madadScore}</td>
                <td className="p-4">{item.riskScore !== null ? item.riskScore : '-'}</td>
                <td className="p-4">
                  <span className={`px-2 py-1 rounded-full text-sm ${RISK_STATUS_STYLES[item.creditStatus]}`}>
                    {item.creditStatus}
                  </span>
                </td>
                <td className="p-4">
                  <button
                    className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                    onClick={() => handleAssess(item)}
                  >
                    Assess Risk
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Credit Risk Assessment Modal */}
      {showReviewModal && selectedInvoice && editedInvoiceData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-7xl h-[90vh] flex">
            {/* Left side - PDF Preview and Company asdasdInfo */}
            <div className="flex-1 pr-6 flex flex-col">
              <h3 className="text-lg font-semibold mb-4">Invoice Preview</h3>
              <iframe
                src={selectedInvoice.signedUrl}
                className="w-full h-1/2 border rounded mb-4"
                title="Invoice PDF"
              />

              <div className="bg-gray-50 p-4 rounded flex-1 overflow-y-auto">
                <h4 className="font-semibold mb-3">Company Financial Health</h4>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <h5 className="font-medium text-sm mb-2">Company History</h5>
                    <p className="text-sm"><span className="text-gray-600">Years in Business:</span> {editedInvoiceData.companyHistory.yearsInBusiness}</p>
                    <p className="text-sm"><span className="text-gray-600">Previous Deals:</span> {editedInvoiceData.companyHistory.previousDeals}</p>
                    <p className="text-sm"><span className="text-gray-600">Repayment History:</span> {editedInvoiceData.companyHistory.repaymentHistory}</p>
                    <p className="text-sm"><span className="text-gray-600">Avg. Days to Pay:</span> {editedInvoiceData.companyHistory.averageDaysToPay}</p>
                  </div>

                  <div>
                    <h5 className="font-medium text-sm mb-2">Customer Reliability</h5>
                    <p className="text-sm"><span className="text-gray-600">Reliability Score:</span> {editedInvoiceData.customerHistory.reliabilityScore}/100</p>
                    <p className="text-sm"><span className="text-gray-600">Years in Business:</span> {editedInvoiceData.customerHistory.yearsInBusiness}</p>
                    <p className="text-sm"><span className="text-gray-600">Previous Transactions:</span> {editedInvoiceData.customerHistory.previousTransactions}</p>
                  </div>
                </div>

                <h5 className="font-medium text-sm mb-2">Financial Metrics</h5>
                <div className="grid grid-cols-4 gap-4">
                  <div className="bg-white p-3 rounded border">
                    <p className="text-xs text-gray-600">Current Ratio</p>
                    <p className="text-lg font-semibold">{editedInvoiceData.financialMetrics.currentRatio}</p>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <p className="text-xs text-gray-600">Debt to Equity</p>
                    <p className="text-lg font-semibold">{editedInvoiceData.financialMetrics.debtToEquity}</p>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <p className="text-xs text-gray-600">Quick Ratio</p>
                    <p className="text-lg font-semibold">{editedInvoiceData.financialMetrics.quickRatio}</p>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <p className="text-xs text-gray-600">ROA</p>
                    <p className="text-lg font-semibold">{editedInvoiceData.financialMetrics.returnOnAssets}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Invoice Details and Risk Assessment */}
            <div className="w-96 flex flex-col">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Credit Assessment</h3>
                <button
                  onClick={() => setShowReviewModal(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="flex-1 space-y-4 overflow-y-auto">
                <div className="bg-gray-50 p-4 rounded">
                  <h4 className="font-semibold mb-2">Invoice Information</h4>
                  <p><span className="text-gray-600">Invoice Number:</span> {editedInvoiceData.invoiceNumber}</p>
                  <p><span className="text-gray-600">Amount:</span> {editedInvoiceData.amount}</p>
                  <p><span className="text-gray-600">Invoice Date:</span> {editedInvoiceData.invoiceDate}</p>
                  <p><span className="text-gray-600">Due Date:</span> {editedInvoiceData.dueDate}</p>
                  <p><span className="text-gray-600">Days until Due:</span> {
                    Math.max(0, Math.floor((new Date(editedInvoiceData.dueDate) - new Date()) / (1000 * 60 * 60 * 24)))
                  }</p>
                </div>

                <div className="bg-gray-50 p-4 rounded">
                  <h4 className="font-semibold mb-2">Company Information</h4>
                  <p><span className="text-gray-600">Company:</span> {editedInvoiceData.company}</p>
                  <p><span className="text-gray-600">Supplier:</span> {editedInvoiceData.supplier}</p>
                  <p><span className="text-gray-600">Customer:</span> {editedInvoiceData.customer}</p>
                  <p><span className="text-gray-600">GSTIN:</span> {editedInvoiceData.gstin}</p>
                </div>

                <div className="bg-gray-50 p-4 rounded">
                  <h4 className="font-semibold mb-2">Risk Assessment</h4>

                  <div className="mb-4">
                    <label className="block text-gray-600 text-sm mb-1">Risk Score (0-100):</label>
                    <div className="flex items-center">
                      <input
                        type="number"
                        className="w-20 p-2 border rounded mr-2"
                        value={editedInvoiceData.riskScore || ''}
                        onChange={(e) => handleRiskScoreChange(parseInt(e.target.value) || '')}
                        min="0"
                        max="100"
                      />
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${editedInvoiceData.riskScore >= 85 ? 'bg-green-500' :
                              editedInvoiceData.riskScore >= 65 ? 'bg-yellow-500' :
                                editedInvoiceData.riskScore >= 40 ? 'bg-orange-500' :
                                  editedInvoiceData.riskScore > 0 ? 'bg-red-500' : 'bg-gray-300'
                            }`}
                          style={{ width: `${editedInvoiceData.riskScore || 0}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className={`w-3 h-3 ${getRiskColorDot(editedInvoiceData.creditStatus)} rounded-full`}></div>
                      <span className={`px-2 py-1 rounded-full text-sm ${RISK_STATUS_STYLES[editedInvoiceData.creditStatus]}`}>
                        {editedInvoiceData.creditStatus}
                      </span>
                    </div>
                  </div>

                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Assessment Notes:</label>
                    <textarea
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.riskAssessmentNotes}
                      onChange={(e) => handleNotesChange(e.target.value)}
                      rows={4}
                      placeholder="Enter your risk assessment comments here..."
                    />
                  </div>
                </div>

                {editedInvoiceData.assessmentHistory && editedInvoiceData.assessmentHistory.length > 0 && (
                  <div className="bg-gray-50 p-4 rounded">
                    <h4 className="font-semibold mb-2">Assessment History</h4>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {editedInvoiceData.assessmentHistory.map((entry, idx) => (
                        <div key={idx} className="text-sm border-b pb-2">
                          <div className="flex justify-between">
                            <span className="text-gray-600">{new Date(entry.date).toLocaleDateString()} {new Date(entry.date).toLocaleTimeString()}</span>
                            <span className={`px-2 py-0.5 rounded-full text-xs ${RISK_STATUS_STYLES[entry.status]}`}>
                              {entry.status}
                            </span>
                          </div>
                          <p className="mt-1">Analyst: {entry.analyst}</p>
                          {entry.reason && <p className="mt-1 italic">{entry.reason}</p>}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <button
                  onClick={handleSaveAssessment}
                  className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 mt-2"
                >
                  Save Assessment
                </button>
              </div>

              <div className="mt-6 pt-6 border-t relative">
                <div className="relative w-full">
                  <button
                    onClick={handleToggleRiskDropdown}
                    className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 flex items-center justify-between"
                  >
                    <span>Set Risk Status</span>
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {showRiskDropdown && (
                    <div className="absolute z-10 bottom-full mb-1 w-full rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                      <div className="py-1" role="menu" aria-orientation="vertical">
                        {riskStatuses.map((status) => (
                          <button
                            key={status}
                            onClick={() => handleRiskSelect(status)}
                            className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${editedInvoiceData.creditStatus === status ? 'bg-blue-50 font-medium' : ''
                              }`}
                            role="menuitem"
                          >
                            <div className="flex items-center">
                              <div className={`w-2 h-2 ${getRiskColorDot(status)} rounded-full mr-2`}></div>
                              {status}
                              {editedInvoiceData.creditStatus === status && (
                                <svg className="w-4 h-4 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                              )}
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Rejection Reason Modal */}
      {showReasonModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Provide Rejection Reason</h3>
            <p className="mb-4 text-sm text-gray-600">Please provide a reason for rejecting this invoice application.</p>

            <textarea
              className="w-full p-3 border rounded mb-4"
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              rows={4}
              placeholder="Enter rejection reason..."
            />

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowReasonModal(false)}
                className="px-4 py-2 border rounded hover:bg-gray-100"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmRejection}
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                disabled={!rejectionReason.trim()}
              >
                Confirm Rejection
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}