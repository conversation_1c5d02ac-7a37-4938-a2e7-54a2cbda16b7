import React, { useState, useEffect, useCallback, useMemo } from 'react';
import axios from 'axios';
import { Disclosure } from '@headlessui/react'; // Import Disclosure
import { FunnelIcon, XMarkIcon, DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';
import { formatPhoneNumber } from '../../utils/phoneFormatter';
import config from "../../../config.json"
import * as XLSX from 'xlsx'; // For Excel export

// --- Reusable Helper Functions ---
const getNested = (obj, path, defaultValue = undefined) => {
  // (Keep the existing getNested function)
  try {
    if (!path || typeof path !== 'string' || !obj) {
      return defaultValue;
    }
    const value = path.split('.').reduce((o, k) => (o && o[k] !== undefined && o[k] !== null) ? o[k] : undefined, obj);
    return (value === undefined) ? defaultValue : value;
  } catch (e) {
    console.error("Error in getNested:", e, "Path:", path, "Object Type:", typeof obj);
    return defaultValue;
  }
};

// Modify the DeactivationModal component definition at the top of your file
const DeactivationModal = ({ isOpen, onClose, onConfirm, creditLineName, onSelectCategory, selectedCategory, isLoading }) => {
  if (!isOpen) return null;

  // Define the options for the deactivation category
  const DEACTIVATION_CATEGORIES = [
    { value: '', label: 'Select Category' }, // Default/placeholder option
    { value: 'Delinquent', label: 'Delinquent (customer behind payment schedule)' },
    { value: 'Fraud', label: 'Fraud (potential fraud investigated or determined)' },
    { value: 'Customer Requested', label: 'Customer Requested (mostly as a result of consent withdrawal)' }
  ];

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-sm mx-auto">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Confirm Deactivation</h3>
        <p className="text-sm text-gray-500 mb-4">
          Select a reason for deactivating the credit line for <span className="font-medium">{creditLineName}</span>. This will change the status to SUSPENDED.
        </p>

        {/* ADD THIS DROPDOWN BLOCK */}
        <div className="mb-6">
          <label htmlFor="deactivationCategory" className="block text-sm font-medium text-gray-700 mb-1">Deactivation Category</label>
          <select
            id="deactivationCategory"
            name="deactivationCategory"
            className="mt-1 block w-full py-2 border border-gray-300 rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm"
            value={selectedCategory}
            onChange={(e) => onSelectCategory(e.target.value)}
            disabled={isLoading} // Disable dropdown while loading
          >
            {DEACTIVATION_CATEGORIES.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {/* Add a simple validation message if no category is selected */}
          {selectedCategory === '' && <p className="mt-2 text-sm text-red-600">Please select a deactivation category.</p>}
        </div>

        <div className="flex justify-end space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
            disabled={isLoading} // Disable cancel button while loading
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className={`px-4 py-2 text-sm font-medium text-white rounded-md border border-transparent ${isLoading || selectedCategory === '' // Disable if loading or no category selected
              ? 'bg-red-400 cursor-not-allowed'
              : 'bg-red-600 hover:bg-red-700'
              }`}
            disabled={isLoading || selectedCategory === ''} // Disable confirm button based on loading and selection
          >
            {isLoading ? 'Deactivating...' : 'Deactivate'}
          </button>
        </div>
      </div>
    </div>
  );
};
// --- Reusable Helper Functions --- (Keep getNested)
// --- Imports --- (Keep React, useState, Disclosure, Icons etc.)

// PASTE THIS NEW FilterSection COMPONENT DEFINITION
const FilterSection = ({ filters, setFilters, resetFilters }) => {
  // Common input styling
  const inputBaseClass = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
  const numberInputClass = `${inputBaseClass} px-2 py-1`;
  const dateInputClass = `${inputBaseClass} px-2 py-1`;
  const textInputClass = `${inputBaseClass} px-3 py-1.5`;

  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prevFilters => ({
      ...prevFilters,
      [name]: value,
    }));
  };

  // Calculate the number of active filters
  const activeFilterCount = useMemo(() => {
    return Object.values(filters).filter(value => value !== '' && value !== null && value !== undefined).length;
  }, [filters]);

  const handleReset = (event) => {
    event.stopPropagation(); // Prevent the disclosure from toggling
    resetFilters();
  };

  return (
    // Add margin below the whole filter section
    <div className="mb-6">
      <Disclosure as="div" className="border border-gray-200 rounded-lg shadow-sm bg-white">
        {({ open }) => (
          <>
            {/* The single Toggable Header Bar - Styled to match example */}
            <div className="flow-root">
              <Disclosure.Button className="flex w-full items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring focus-visible:ring-indigo-500 focus-visible:ring-opacity-75">
                {/* Left side: Icon, Text, Count */}
                <span className="flex items-center">
                  <FunnelIcon className="mr-2 h-5 w-5 text-gray-400" aria-hidden="true" />
                  Filters
                  {activeFilterCount > 0 && (
                    <span className="ml-2 rounded-full bg-gray-200 px-2 py-0.5 text-xs font-medium text-gray-800">
                      {activeFilterCount}
                    </span>
                  )}
                </span>

                {/* Right side: Clear button OR Expand/Collapse Icon - Example implicitly uses the whole bar click */}
                <span className="ml-6 flex items-center">
                  {/* Optional: Show Chevron only if preferred over whole bar click */}
                  {/* <ChevronUpIcon
                        className={`${open ? 'rotate-180 transform' : ''} h-5 w-5 text-gray-500 transition-transform duration-150 ease-in-out`}
                    /> */}
                </span>
              </Disclosure.Button>
            </div>

            {/* Separator Line for the Panel */}
            {open && <div className="border-t border-gray-200"></div>}

            {/* The Panel containing all filter inputs */}
            <Disclosure.Panel className="px-4 py-5 sm:px-6 lg:px-8">
              {/* Top row with search and maybe clear button */}
              <div className="mb-4 flex items-start justify-between">
                <div className="flex-1 mr-4"> {/* Search takes most space */}
                  <label htmlFor="searchTerm" className="sr-only">Search (Name)</label>
                  <input
                    type="text"
                    name="searchTerm"
                    id="searchTerm"
                    value={filters.searchTerm}
                    onChange={handleFilterChange}
                    className={textInputClass}
                    placeholder="Search Borrower or Business..."
                  />
                </div>
                {/* Clear button moved next to search */}
                <button
                  type="button"
                  onClick={handleReset} // Use the wrapper function
                  className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                  <XMarkIcon className="-ml-1 mr-1.5 h-4 w-4 text-gray-400" aria-hidden="true" />
                  Clear all
                </button>
              </div>

              {/* Grid for the rest of the filters */}
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">

                {/* Offer Limit Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Offer Limit (QAR)</label>
                  <div className="flex space-x-2">
                    <input type="number" name="minLimit" value={filters.minLimit} onChange={handleFilterChange} placeholder="Min" className={numberInputClass} />
                    <input type="number" name="maxLimit" value={filters.maxLimit} onChange={handleFilterChange} placeholder="Max" className={numberInputClass} />
                  </div>
                </div>

                {/* Interest Rate Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Service Fee (%)</label>
                  <div className="flex space-x-2">
                    <input type="number" name="minRate" value={filters.minRate} onChange={handleFilterChange} placeholder="Min" step="0.01" className={numberInputClass} />
                    <input type="number" name="maxRate" value={filters.maxRate} onChange={handleFilterChange} placeholder="Max" step="0.01" className={numberInputClass} />
                  </div>
                </div>

                {/* Tenure Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tenure (Days)</label>
                  <div className="flex space-x-2">
                    <input type="number" name="minTenure" value={filters.minTenure} onChange={handleFilterChange} placeholder="Min" step="1" className={numberInputClass} />
                    <input type="number" name="maxTenure" value={filters.maxTenure} onChange={handleFilterChange} placeholder="Max" step="1" className={numberInputClass} />
                  </div>
                </div>

                {/* Date Range */}
                <div className="sm:col-span-2 md:col-span-1"> {/* Adjust span as needed */}
                  <label className="block text-sm font-medium text-gray-700 mb-1">Last Updated Date</label>
                  <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:items-center sm:space-x-2">
                    <input type="date" name="startDate" value={filters.startDate} onChange={handleFilterChange} className={dateInputClass + ' flex-1'} />
                    <span className="text-gray-500 text-center hidden sm:inline">to</span>
                    <input type="date" name="endDate" value={filters.endDate} onChange={handleFilterChange} className={dateInputClass + ' flex-1'} />
                  </div>
                </div>
              </div>
            </Disclosure.Panel>
          </>
        )}
      </Disclosure>
      {/* REMOVED ActiveFilterTags component - it's not in the target UI example */}
    </div>
  );
};
// END OF NEW FilterSection COMPONENT


// REMOVE the ActiveFilterTags component definition entirely from your file
// const ActiveFilterTags = ({ filters, setFilters }) => { ... }; // DELETE THIS


// --- formatDate / getNested --- (Keep as is)

// --- CreditLineActivationPage ---
// REMEMBER: You still need the changes inside CreditLineActivationPage mentioned before:
// 1. REMOVE the `showFilters` state variable.
// 2. REMOVE the `showFilters` and `setShowFilters` props passed to `<FilterSection>`.
// 3. The `<FilterSection>` call should just be:
//    <FilterSection filters={filters} setFilters={setFilters} resetFilters={resetFilters} />
//    (The margin `mb-6` is now handled *inside* the FilterSection component's wrapper div).

// Component to display applied filters as tags
// PASTE THIS NEW ActiveFilterTags COMPONENT DEFINITION
// const ActiveFilterTags = ({ filters, setFilters }) => {
//   // Check if any filter is active
//   const hasActiveFilters = Object.entries(filters).some(([key, value]) => {
//     if (typeof value === 'string') return value.trim() !== '';
//     return value !== '';
//   });

//   if (!hasActiveFilters) return null;

//   // Function to remove a single filter
//   const removeFilter = (filterKey) => {
//     setFilters(prev => ({ ...prev, [filterKey]: '' }));
//   };

//   return (
//     <div className="bg-gray-50 py-3 px-4 sm:px-6 lg:px-8 border-t border-gray-200 rounded-b-lg"> {/* Adjusted styling slightly */}
//       <div className="flex flex-wrap items-center gap-2">
//         <span className="text-sm font-medium text-gray-700">Active filters:</span>

//         {filters.searchTerm && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200"> {/* Adjusted tag style */}
//             Search: {filters.searchTerm}
//             <button
//               onClick={() => removeFilter('searchTerm')}
//               className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none"
//             >
//               <span className="sr-only">Remove filter</span>
//               <XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}

//         {/* Repeat similar span structure for all other filters... */}
//         {/* Example for minLimit */}
//         {filters.minLimit && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Min Limit: {filters.minLimit}
//             <button onClick={() => removeFilter('minLimit')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span>
//               <XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {/* ... and maxLimit, minRate, maxRate, minTenure, maxTenure, startDate, endDate */}
//         {filters.maxLimit && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Max Limit: {filters.maxLimit}
//             <button onClick={() => removeFilter('maxLimit')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.minRate && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Min Rate: {filters.minRate}%
//             <button onClick={() => removeFilter('minRate')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.maxRate && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Max Rate: {filters.maxRate}%
//             <button onClick={() => removeFilter('maxRate')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.minTenure && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Min Tenure: {filters.minTenure} days
//             <button onClick={() => removeFilter('minTenure')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.maxTenure && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Max Tenure: {filters.maxTenure} days
//             <button onClick={() => removeFilter('maxTenure')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.startDate && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             From: {filters.startDate}
//             <button onClick={() => removeFilter('startDate')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.endDate && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             To: {filters.endDate}
//             <button onClick={() => removeFilter('endDate')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}

//       </div>
//     </div>
//   );
// };
// END OF NEW ActiveFilterTags COMPONENT

const formatDate = (dateString) => {
  // (Keep the existing formatDate function)
  if (!dateString || dateString === 'N/A' || dateString === undefined) return 'N/A';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime()) || date.getFullYear() <= 1970) return 'N/A';
    return date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return 'Invalid Date';
  }
};
// --- End Helper Functions ---

// =========================================================================
//  MAIN CREDIT LINE ACTIVATION PAGE COMPONENT
// =========================================================================
export default function CreditLineActivationPage() {
  // --- State Variables ---
  const [allActivations, setAllActivations] = useState([]);
  const [pendingActivations, setPendingActivations] = useState([]);
  const [activeActivations, setActiveActivations] = useState([]);

  const [loading, setLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const [activeTab, setActiveTab] = useState('all');
  const [activatingId, setActivatingId] = useState(null);
  const [deactivatingId, setDeactivatingId] = useState(null);
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [selectedCreditLine, setSelectedCreditLine] = useState(null);
  const [deactivationCategory, setDeactivationCategory] = useState(''); // To store selected category for deactivation modal
  const [lenderDetailsMap, setLenderDetailsMap] = useState({});

  const openDeactivateModal = (creditLine) => {
    setSelectedCreditLine(creditLine);
    setDeactivationCategory(''); // ADD THIS LINE to reset the category when opening the modal
    setShowDeactivateModal(true);
  };


  // --- Data Filtering Logic for Tabs ---
  const filterActivationTabs = useCallback((activations) => {
    setPendingActivations(activations.filter(a => a.creditLineStatus === 'APPROVED' || a.creditLineStatus === 'SUSPENDED'));
    setActiveActivations(activations.filter(a => a.creditLineStatus === 'ACTIVE'));
  }, []);
  // --- State for Filters ---
  const [filters, setFilters] = useState({
    searchTerm: '',
    minLimit: '',
    maxLimit: '',
    minRate: '',
    maxRate: '',
    minTenure: '',
    maxTenure: '',
    startDate: '',
    endDate: '',
  });
  // const [showFilters, setShowFilters] = useState(false);

  const resetFilters = React.useCallback(() => { // Assuming React is imported
    setFilters({
      searchTerm: '', minLimit: '', maxLimit: '', minRate: '', maxRate: '',
      minTenure: '', maxTenure: '', startDate: '', endDate: '',
    });
  }, []);



  // --- Filter Input Change Handler ---
  // const handleFilterChange = (event) => {
  //   const { name, value } = event.target;
  //   setFilters(prevFilters => ({
  //     ...prevFilters,
  //     [name]: value,
  //   }));
  // };

  // --- Data Fetching Function ---
  const fetchData = useCallback(async () => {
    console.log("fetchData called for Admin Credit Line Activation Page");
    setLoading(true); // Ensure loading is true at the start of any fetch
    setFetchError(null);

    try {
      const [clResult, userResult, offerResult] = await Promise.allSettled([
        axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLines`),
        axios.get(`${config.apiUrl}/ops/invoiceFinancing/getSubmittedKycs`),
        axios.get(`${config.apiUrl}/ops/invoiceFinancing/offers?offerType=creditLineOffer`)
      ]);

      // --- Process Responses ---
      let fetchedCreditLines = [];
      if (clResult.status === 'fulfilled' && clResult.value.data) {
        fetchedCreditLines = Array.isArray(clResult.value.data) ? clResult.value.data : (clResult.value.data.creditLines || []);
        console.log("Fetched credit lines (Admin):", fetchedCreditLines.length);
      } else {
        console.error("Failed to fetch credit lines (Admin)", clResult.reason || clResult.value?.statusText || clResult.value?.status);
      }

      let fetchedUsersMap = new Map();
      if (userResult.status === 'fulfilled' && userResult.value.data?.success && Array.isArray(userResult.value.data.kycs)) {
        fetchedUsersMap = new Map(userResult.value.data.kycs.map(user => [user._id, user]));
      } else {
        console.error("Failed to fetch users", userResult.reason || userResult.value?.statusText || userResult.value?.status);
      }

      let fetchedOffers = [];
      if (offerResult.status === 'fulfilled' && offerResult.value.data?.success && Array.isArray(offerResult.value.data.offers)) {
        fetchedOffers = offerResult.value.data.offers;
      } else {
        console.warn("Failed to fetch offers", offerResult.reason || offerResult.value?.statusText || offerResult.value?.status);
      }

      // --- Combine and Filter Data ---
      const relevantActivations = [];

      // --- CORRECTED offersMap CREATION ---
      const offerEntries = fetchedOffers
        .map(o => {
          // Ensure merchantId and lenderId are present and not undefined
          if (o.merchantId === undefined || o.lenderId === undefined || o.merchantId === null || o.lenderId === null) {
            console.warn("Offer with undefined or null merchantId or lenderId, skipping:", o);
            return null; // This entry will be filtered out
          }
          const key = `${o.merchantId}-${o.lenderId}`;
          return [key, o];
        })
        .filter(Boolean); // Filter out any null entries from the map step

      const offersMap = new Map(offerEntries);
      // --- END OF CORRECTED offersMap CREATION ---

      fetchedCreditLines.forEach(cl => {
        const clStatus = String(cl.creditLineStatus || '').toUpperCase();
        const isRelevantStatus = clStatus === 'APPROVED' || clStatus === 'ACTIVE' || clStatus === 'SUSPENDED';
        const isOfferAccepted = cl.offerAccepted === true;

        if (isRelevantStatus && isOfferAccepted) {
          const userDetails = fetchedUsersMap.get(cl.userId);

          // Construct the key carefully, ensuring cl.userId and cl.lenderId are valid
          let offerKey = null;
          if (cl.userId && cl.lenderId) {
            offerKey = `${cl.userId}-${cl.lenderId}`;
          } else {
            console.warn(`CreditLine ${cl._id} has missing userId or lenderId. Cannot form offerKey.`);
          }

          const offerDetails = offerKey ? offersMap.get(offerKey) : undefined;

          if (userDetails && offerDetails) {
            relevantActivations.push({ ...cl, userDetails, offerDetails });
          } else {
            console.warn(`Skipping CL ${cl._id} (Status: ${clStatus}, Lender: ${cl.lenderId}, UserID: ${cl.userId}): Missing -> ${!userDetails ? 'User Details ' : ''}${!offerDetails ? `Offer Details (Key: ${offerKey})` : ''}`);
            if (!userDetails) console.warn(` > User Map does not have key: ${cl.userId}`);
            if (!offerDetails && offerKey) console.warn(` > Offer Map does not have key: ${offerKey}`);
          }
        }
      });

      console.log(`Found ${relevantActivations.length} relevant activations (Admin View)`);
      setAllActivations(relevantActivations);
      filterActivationTabs(relevantActivations);

    } catch (error) {
      console.error('Error during fetchData on Admin Activation Page:', error);
      setFetchError(error.message || "An unknown error occurred.");
      setAllActivations([]);
      filterActivationTabs([]);
    } finally {
      setLoading(false);
    }
  }, [filterActivationTabs]);

  const handleDeactivate = useCallback(async (creditLine) => {
    if (!creditLine?._id || !creditLine?.userId) {
      alert("Error: Missing necessary information to deactivate.");
      return;
    }

    if (!deactivationCategory) {
      alert("Please select a deactivation category.");
      return; // Stop execution if no category is selected
    }

    setDeactivatingId(creditLine._id);

    const payload = {
      userId: creditLine.userId,
      creditLineData: {
        _id: creditLine._id,
        creditLineStatus: 'SUSPENDED',
        deactivationCategory: deactivationCategory,
      }
    };
    console.log("Deactivation Payload:", JSON.stringify(payload, null, 2));

    try {
      const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/creditLineCreateOrUpdate`, payload);

      if (!response.data?.success && !response.data?.creditLine) {
        throw new Error(response.data?.message || "Backend reported failure during deactivation.");
      }

      console.log("Deactivation successful for:", creditLine._id);
      alert(`Credit line deactivated successfully! Category: ${deactivationCategory}`);
      await fetchData(); // Refresh the entire list

    } catch (error) {
      console.error('Error deactivating credit line:', error.response?.data || error.message);
      alert(`Error deactivating credit line: ${getNested(error, 'response.data.message', error.message) || 'An unknown error occurred'}`);
    } finally {
      setDeactivatingId(null);
      setShowDeactivateModal(false);
      setDeactivationCategory('');
    }
  }, [fetchData, deactivationCategory]); // IMPORTANT: ADD deactivationCategory to the dependency array

  // --- Initial Data Fetch ---
  useEffect(() => {
    const fetchInitialData = async () => {
      setLoading(true);
      await fetchAllLenderDetails(); // Fetch lender details first or in parallel
      await fetchData(); // Then fetch credit line data
      setLoading(false);
    };
    fetchInitialData();
  }, [fetchData]); // Keep fetchData in dependency array if it's memoized

  // NEW: Effect to fetch all lender details on mount
  const fetchAllLenderDetails = useCallback(async () => { // Wrapped in useCallback
    let tempError = null;
    try {
      console.log("Attempting to fetch all lender details for CreditLineActivationPage...");
      const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/lender-admins`); // Or your correct 'all lenders' endpoint
      console.log("Lender details API Response (CreditLineActivationPage):", response);

      if (response.data && Array.isArray(response.data.data)) { // Assuming data is in response.data.data
        const lendersArray = response.data.data;
        const map = lendersArray.reduce((acc, lender) => {
          if (lender && lender._id) {
            acc[lender._id] = {
              lenderName: lender.lenderName || 'Unnamed Lender',
              logoUrl: lender.logoUrl || null
            };
          }
          return acc;
        }, {});
        setLenderDetailsMap(map);
        console.log("Lender details map created (CreditLineActivationPage):", map);
      } else {
        console.warn("Could not fetch or parse lender details for CreditLineActivationPage. Expected 'response.data.data' to be an array. Received:", response.data);
        tempError = "Failed to parse lender details from API response.";
        setLenderDetailsMap({});
      }
    } catch (err) {
      console.error('Error fetching lender details (CreditLineActivationPage):', err);
      const errorMessage = `Failed to load lender details: ${err.response?.data?.message || err.message || 'Unknown error'}`;
      tempError = errorMessage;
      setLenderDetailsMap({});
    }
    if (tempError && !fetchError) { // Only set if no primary fetchError yet
      setFetchError(prev => prev ? `${prev}\n${tempError}` : tempError);
    }
  }, [fetchError]); // Added fetchError to dependency to avoid stale closure if error occurs then retried

  // --- Handle Activate Button Click ---
  const handleActivate = useCallback(async (creditLine) => {
    // (Keep the existing handleActivate function - it's correct)
    if (!creditLine?._id || !creditLine?.userId) {
      alert("Error: Missing necessary information to activate.");
      return;
    }
    if (creditLine.creditLineStatus !== 'APPROVED' && creditLine.creditLineStatus !== 'SUSPENDED') {
      alert("Credit line is not in approved or deactivated status.");
      return;
    }

    setActivatingId(creditLine._id);

    const payload = {
      userId: creditLine.userId,
      creditLineData: {
        _id: creditLine._id,
        creditLineStatus: 'ACTIVE'
      }
    };
    console.log("Activation Payload:", JSON.stringify(payload, null, 2));

    try {
      const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/creditLineCreateOrUpdate`, payload);

      if (!response.data?.success && !response.data?.creditLine) {
        throw new Error(response.data?.message || "Backend reported failure during activation.");
      }

      console.log("Activation successful for:", creditLine._id);
      alert("Credit line activated successfully!");
      await fetchData(); // Refresh the entire list

    } catch (error) {
      console.error('Error activating credit line:', error.response?.data || error.message);
      alert(`Error activating credit line: ${getNested(error, 'response.data.message', error.message) || 'An unknown error occurred'}`);
    } finally {
      setActivatingId(null);
    }
  }, [fetchData]);

  const currentTableData = useMemo(() => {
    let sourceData;
    if (activeTab === 'all') {
      sourceData = allActivations;
    } else if (activeTab === 'pending') {
      sourceData = pendingActivations;
    } else { // 'activated'
      sourceData = activeActivations;
    }

    if (!sourceData || sourceData.length === 0) return [];

    const { searchTerm, minLimit, maxLimit, minRate, maxRate, minTenure, maxTenure, startDate, endDate } = filters;

    // Prepare filter values once
    const lowerSearchTerm = searchTerm.toLowerCase();
    const numMinLimit = minLimit === '' ? -Infinity : parseFloat(minLimit);
    const numMaxLimit = maxLimit === '' ? Infinity : parseFloat(maxLimit);
    const numMinRate = minRate === '' ? -Infinity : parseFloat(minRate);
    const numMaxRate = maxRate === '' ? Infinity : parseFloat(maxRate);
    const numMinTenure = minTenure === '' ? -Infinity : parseInt(minTenure, 10);
    const numMaxTenure = maxTenure === '' ? Infinity : parseInt(maxTenure, 10);
    // Get timestamp for start/end of day for date comparison
    const tsStartDate = startDate ? new Date(startDate).setHours(0, 0, 0, 0) : null;
    const tsEndDate = endDate ? new Date(endDate).setHours(23, 59, 59, 999) : null;

    // Filter the source data
    return sourceData.filter(cl => {
      const borrowerName = `${getNested(cl, 'userDetails.firstName', '')} ${getNested(cl, 'userDetails.lastName', '')}`.toLowerCase();
      const businessName = getNested(cl, 'userDetails.kyc.businessDetails.businessName', '').toLowerCase();
      const limit = parseFloat(getNested(cl, 'offerDetails.creditLimit', NaN)); // Default to NaN if missing
      const rate = parseFloat(getNested(cl, 'offerDetails.interestRate', NaN));
      const tenure = parseInt(getNested(cl, 'offerDetails.tenureDays', NaN), 10);
      const updatedAtTs = cl.updatedAt ? new Date(cl.updatedAt).getTime() : null;

      // Apply Filters - return false if ANY filter fails
      if (lowerSearchTerm && !(borrowerName.includes(lowerSearchTerm) || businessName.includes(lowerSearchTerm))) {
        return false;
      }
      if (!isNaN(numMinLimit) && (isNaN(limit) || limit < numMinLimit)) { return false; }
      if (!isNaN(numMaxLimit) && (isNaN(limit) || limit > numMaxLimit)) { return false; }
      if (!isNaN(numMinRate) && (isNaN(rate) || rate < numMinRate)) { return false; }
      if (!isNaN(numMaxRate) && (isNaN(rate) || rate > numMaxRate)) { return false; }
      if (!isNaN(numMinTenure) && (isNaN(tenure) || tenure < numMinTenure)) { return false; }
      if (!isNaN(numMaxTenure) && (isNaN(tenure) || tenure > numMaxTenure)) { return false; }
      if (tsStartDate && (!updatedAtTs || updatedAtTs < tsStartDate)) { return false; }
      if (tsEndDate && (!updatedAtTs || updatedAtTs > tsEndDate)) { return false; }

      return true; // Passed all filters
    });
  }, [filters, activeTab, allActivations, pendingActivations, activeActivations]);

  // --- Function to Render Table Rows ---
  const renderTableRows = (data) => {
    if (!data || data.length === 0) {
      return (
        <tr>
          {/* MODIFIED: Adjust colSpan to include new Lender column */}
          <td colSpan="8" className="text-center py-10 px-4 text-sm text-gray-500 italic">
            No credit lines found matching the current filters in this section.
          </td>
        </tr>
      );
    }

    return data.map((cl) => {
      const offerDetails = cl.offerDetails || {};
      const contractUrl = cl.offerDetails?.facilityContract?.signedUrl ?? null; // Keep null if no default
      const isActivatingThis = activatingId === cl._id;
      const isCurrentDeactivating = deactivatingId === cl._id; // To disable deactivation button while processing

      // NEW: Get lender details for the current credit line item
      const lenderInfo = lenderDetailsMap[cl.lenderId] || { lenderName: 'N/A', logoUrl: null };

      return (
        <tr key={cl._id} className="hover:bg-gray-50">
          {/* NEW: Lender Column */}
          <td className="px-4 py-3 whitespace-nowrap">
            <div className="flex items-center">
              {lenderInfo.logoUrl ? (
                <Image
                  unoptimized
                  src={lenderInfo.logoUrl}
                  alt={`${lenderInfo.lenderName} Logo`}
                  width={24} // Adjust size as needed
                  height={24}
                  className="h-6 w-6 rounded-full mr-2 object-contain flex-shrink-0"
                />
              ) : (
                <span className="h-6 w-6 rounded-full bg-gray-300 mr-2 flex items-center justify-center text-xs text-gray-500">
                  {lenderInfo.lenderName !== 'N/A' ? lenderInfo.lenderName.substring(0, 1) : '?'}
                </span>
              )}
              <div className="text-sm font-medium text-gray-900 truncate" title={lenderInfo.lenderName}>
                {lenderInfo.lenderName}
              </div>
            </div>
            <div className="text-xs text-gray-500 font-mono mt-0.5 truncate" title={cl.lenderId}>{cl.lenderId}</div>
          </td>
          {/* Business Name */}
          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
            {getNested(cl, 'userDetails.kyc.businessDetails.businessName', 'N/A')}
          </td>
          {/* Borrower Name */}
          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
            <div className="font-medium">{[getNested(cl, 'userDetails.firstName'), getNested(cl, 'userDetails.lastName')].filter(Boolean).join(' ')}</div>
            <div className="text-xs text-gray-500">{getNested(cl, 'userDetails.email', 'N/A')}</div>
          </td>
          {/* Borrower Phone */}
          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
            {formatPhoneNumber(getNested(cl, 'userDetails.mobileNo', 'N/A'))}
          </td>
          {/* Last Updated Date */}
          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
            {formatDate(cl.updatedAt)}
          </td>
          {/* Offer Details */}
          <td className="px-4 py-3 whitespace-normal text-xs text-gray-700 align-top">
            <div className="flex flex-col space-y-0.5">
              <span>Limit: <span className="font-medium">{getNested(offerDetails, 'creditLimit', 'N/A')?.toLocaleString()} {cl.currency || 'QAR'}</span></span>
              <span>Tenure: <span className="font-medium">{getNested(offerDetails, 'tenureDays', 'N/A')} Days</span></span>
              <span>Service Fee: <span className="font-medium">{getNested(offerDetails, 'interestRate', 'N/A')}%</span></span>
            </div>
          </td>
          {/* Contract */}
          <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
            {contractUrl ? (
              <a
                href={contractUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-indigo-600 hover:text-indigo-900 hover:underline inline-flex items-center"
                title="View Contract PDF"
              >
                View
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            ) : (
              <span className="text-gray-400 italic text-xs">N/A</span>
            )}
          </td>
          {/* Actions */}
          <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
            {(cl.creditLineStatus === 'APPROVED' || cl.creditLineStatus === 'SUSPENDED') && (
              <button
                onClick={() => handleActivate(cl)}
                disabled={isActivatingThis || isCurrentDeactivating} // Disable if another action is in progress for this item
                className={`inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white
                ${(isActivatingThis || isCurrentDeactivating) ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'}`}
              >
                {isActivatingThis ? ( /* ... spinner ... */ 'Activating...') : 'Activate'}
              </button>
            )}
            {cl.creditLineStatus === 'ACTIVE' && (
              <button
                onClick={() => openDeactivateModal(cl)}
                disabled={isCurrentDeactivating || isActivatingThis} // Disable if another action is in progress for this item
                className={`inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white
                                  ${(isCurrentDeactivating || isActivatingThis) ? 'bg-gray-400 cursor-not-allowed' : 'bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'}`}
              >
                {isCurrentDeactivating ? ( /* ... spinner ... */ 'Deactivating...') : 'Deactivate'}
              </button>
            )}
          </td>
        </tr>
      );
    });
  };

  // --- Common Input Styling ---
  // const inputBaseClass = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
  // const numberInputClass = `${inputBaseClass} px-2 py-1`;
  // const dateInputClass = `${inputBaseClass} px-2 py-1`;
  // const textInputClass = `${inputBaseClass} px-3 py-1.5`;

  // --- MAIN RENDER ---


  // --- ADD THIS FUNCTION TO HANDLE EXCEL EXPORT ---
  const handleExportToExcel = useCallback(() => {
    if (!currentTableData || currentTableData.length === 0) {
      alert("No data to export.");
      return;
    }

    const dataForExcel = currentTableData.map(cl => {
      const lenderInfo = lenderDetailsMap[cl.lenderId] || { lenderName: 'N/A', logoUrl: null };
      const offerDetails = cl.offerDetails || {};
      const contractUrl = offerDetails?.facilityContract?.signedUrl ?? 'N/A';

      return {
        'Lender Name': lenderInfo.lenderName,
        'Lender ID': cl.lenderId,
        'Business Name': getNested(cl, 'userDetails.kyc.businessDetails.businessName', 'N/A'),
        'Borrower Name': [getNested(cl, 'userDetails.firstName', ''), getNested(cl, 'userDetails.lastName', '')].filter(Boolean).join(' '),
        'Borrower Email': getNested(cl, 'userDetails.email', 'N/A'),
        'Borrower Phone': getNested(cl, 'userDetails.mobileNo', 'N/A'),
        'Last Updated': formatDate(cl.updatedAt), // Uses existing formatDate
        'Offer Limit': getNested(offerDetails, 'creditLimit', 'N/A'),
        'Offer Currency': cl.currency || 'QAR',
        'Tenure (Days)': getNested(offerDetails, 'tenureDays', 'N/A'),
        'Service Fee (%)': getNested(offerDetails, 'interestRate', 'N/A'),
        'Credit Line Status': cl.creditLineStatus,
        'Contract Link': contractUrl === 'N/A' ? 'N/A' : contractUrl,
      };
    });

    const worksheet = XLSX.utils.json_to_sheet(dataForExcel);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Activations");

    // Generate filename with timestamp
    const now = new Date();
    const timestampStr = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`;
    XLSX.writeFile(workbook, `CreditLineActivations_${timestampStr}.xlsx`);
  }, [currentTableData, lenderDetailsMap]); // Dependencies: currentTableData and lenderDetailsMap


  return (
    <div className="p-4 md:p-6 bg-gray-100 min-h-screen">
      <h1 className="text-2xl font-bold mb-4 text-gray-800">Credit Line Activation</h1>

      {/* Filter Component */}
      <div className="mb-6"> {/* Optional: Add margin below filters */}
        <FilterSection
          filters={filters}
          setFilters={setFilters}
          // No showFilters/setShowFilters needed here
          resetFilters={resetFilters}
        />
      </div>

      <div className="mb-4 flex justify-end">
        <button
          onClick={handleExportToExcel}
          disabled={loading || !currentTableData || currentTableData.length === 0} // Disable if loading or no data
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          <DocumentArrowDownIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
          Export to Excel
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="mb-6 border-b border-gray-200">
        <nav className="-mb-px flex space-x-6 overflow-x-auto" aria-label="Tabs">
          <button onClick={() => setActiveTab('all')}
            className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTab === 'all' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          >
            All ({loading ? '...' : allActivations.length}) {/* Show total count before filtering */}
          </button>
          <button onClick={() => setActiveTab('pending')}
            className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTab === 'pending' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          >
            Pending Activation ({loading ? '...' : pendingActivations.length})
          </button>
          <button onClick={() => setActiveTab('activated')}
            className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTab === 'activated' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          >
            Activated ({loading ? '...' : activeActivations.length})
          </button>
        </nav>
      </div>

      {/* Loading & Error Display */}
      {loading && !fetchError && (
        <div className="text-center py-10 text-gray-500 italic">Loading activation data...</div>
      )}
      {fetchError && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{fetchError}</span>
          <button onClick={fetchData} className="ml-4 py-1 px-2 border border-red-300 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200">
            Retry Fetch
          </button>
        </div>
      )}

      {/* Table Display */}
      {!loading && !fetchError && (
        <div className="shadow overflow-x-auto border-b border-gray-200 sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lender</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Borrower</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer Details</th>
                <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Contract</th>
                <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {/* Use the memoized and filtered data */}
              {renderTableRows(currentTableData)}
            </tbody>
          </table>
        </div>
      )}
      <DeactivationModal
        isOpen={showDeactivateModal}
        onClose={() => setShowDeactivateModal(false)}
        onConfirm={() => handleDeactivate(selectedCreditLine)}
        creditLineName={getNested(selectedCreditLine, 'userDetails.kyc.businessDetails.businessName', 'this credit line')}
        onSelectCategory={setDeactivationCategory}
        selectedCategory={deactivationCategory}
        isLoading={deactivatingId !== null} // Pass the loading state to the modal
      />
    </div>
  );
}