import React from 'react';
import Link from 'next/link';
import { useAuth } from '../../contexts/authContext';
import {
  DocumentMagnifyingGlassIcon,
  UserGroupIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';

const Reports = () => {
  const { user } = useAuth();

  const reportSections = [
    {
      title: 'Features and Defects',
      description: 'Track and analyze system features and defects',
      icon: DocumentMagnifyingGlassIcon,
      path: '/featuresTracker',
      roles: ['buyerAdmin', 'lenderAdmin', 'complianceOfficer', 'superadmin'],
      color: 'bg-blue-500 hover:bg-blue-600',
      iconBg: 'bg-blue-100',
      iconColor: 'text-blue-600'
    },
    {
      title: 'Customer Report',
      description: 'Comprehensive customer analytics and insights',
      icon: UserGroupIcon,
      path: '/customerReport',
      roles: ['superadmin', 'buyerAdmin', 'lenderAdmin', 'complianceOfficer'],
      color: 'bg-green-500 hover:bg-green-600',
      iconBg: 'bg-green-100',
      iconColor: 'text-green-600'
    },
    {
      title: 'Buyer Report',
      description: 'Detailed buyer performance and statistics',
      icon: BuildingOfficeIcon,
      path: '/buyerReport',
      roles: ['superadmin', 'buyerAdmin', 'lenderAdmin', 'complianceOfficer'],
      color: 'bg-purple-500 hover:bg-purple-600',
      iconBg: 'bg-purple-100',
      iconColor: 'text-purple-600'
    },
    {
      title: 'Customer Complaints',
      description: 'Monitor and manage customer complaints and feedback',
      icon: UserGroupIcon,
      path: '/customerComplaints',
      roles: ['superadmin', 'buyerAdmin', 'lenderAdmin', 'complianceOfficer'],
      color: 'bg-red-500 hover:bg-red-600',
      iconBg: 'bg-red-100',
      iconColor: 'text-red-600'
    }
  ];

  // Filter reports based on user role
  const availableReports = reportSections.filter(report =>
    report.roles.includes(user?.role)
  );

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Reports Dashboard</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Access comprehensive reports and analytics to monitor your business operations
          </p>
        </div>

        {/* Reports Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {availableReports.map((report) => {
            const IconComponent = report.icon;

            return (
              <div key={report.title} className="block group">
                <Link href={report.path}>
                  <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 p-6 h-full border border-gray-200 cursor-pointer">
                    {/* Icon */}
                    <div className={`inline-flex p-3 rounded-lg ${report.iconBg} mb-4`}>
                      <IconComponent className={`h-8 w-8 ${report.iconColor}`} />
                    </div>

                    {/* Content */}
                    <div className="mb-6">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-gray-700">
                        {report.title}
                      </h3>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {report.description}
                      </p>
                    </div>

                    {/* CTA Button */}
                    <div className="mt-auto">
                      <div className={`inline-flex items-center px-4 py-2 rounded-lg text-white text-sm font-medium transition-colors ${report.color}`}>
                        View Report
                        <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            );
          })}
        </div>

        {/* Empty State */}
        {availableReports.length === 0 && (
          <div className="text-center py-12">
            <DocumentMagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No reports available</h3>
            <p className="mt-1 text-sm text-gray-500">
              You do not have access to any reports with your current role.
            </p>
          </div>
        )}

        {/* Quick Stats */}

      </div>
    </div>
  );
};

export default Reports;