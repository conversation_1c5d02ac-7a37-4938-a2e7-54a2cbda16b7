import React, { useState, useEffect, useMemo } from 'react';
import * as XLSX from 'xlsx';
import config from "../../../config.json";
import Link from 'next/link';

// Helper function to format dates
const formatDate = (dateString) => {
    if (!dateString) {
        return 'N/A';
    }
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return 'Invalid Date';
        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
        });
    } catch (e) {
        console.log(e)
        return 'Invalid Date';
    }
};

// Simple status badge component
const OnboardingStatusBadge = ({ status }) => {
    const statusClasses = {
        'Onboarded': 'bg-green-100 text-green-800',
        'Non-Onboarded': 'bg-blue-100 text-blue-800',
    };
    const style = statusClasses[status] || 'bg-gray-100 text-gray-800';

    return (
        <span className={`px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${style}`}>
            {status}
        </span>
    );
};


export default function BuyerReport() {
    const [buyersData, setBuyersData] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [isExporting, setIsExporting] = useState(false);

    const [filters, setFilters] = useState({
        searchTerm: '',
        onboardingStatus: 'All', // 'All', 'Onboarded', 'Non-Onboarded'
    });

    const [sortConfig, setSortConfig] = useState({ key: 'dateOnboarded', direction: 'descending' });

    useEffect(() => {
        const fetchBuyerData = async () => {
            setIsLoading(true);
            setError(null);
            try {
                const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/buyers/all-with-msme`);
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: `Request failed with status ${response.status}` }));
                    throw new Error(errorData.message);
                }
                const data = await response.json();
                setBuyersData(data);
            } catch (err) {
                setError(err.message || 'An unexpected error occurred.');
            } finally {
                setIsLoading(false);
            }
        };
        fetchBuyerData();
    }, []);

    const handleFilterChange = (e) => {
        const { name, value } = e.target;
        setFilters(prev => ({ ...prev, [name]: value }));
    };

    const filteredAndSortedBuyers = useMemo(() => {
        let filtered = [...buyersData];

        if (filters.onboardingStatus !== 'All') {
            filtered = filtered.filter(buyer => buyer.onboardingStatus === filters.onboardingStatus);
        }

        if (filters.searchTerm) {
            const term = filters.searchTerm.toLowerCase();
            filtered = filtered.filter(buyer =>
                (buyer.madadId?.toLowerCase().includes(term)) ||
                (buyer.tradeName?.toLowerCase().includes(term)) ||
                (buyer.msmeAssociatedWithName?.toLowerCase().includes(term)) ||
                (buyer.applicantName?.toLowerCase().includes(term)) ||
                (buyer.crNumber?.toLowerCase().includes(term))
            );
        }

        if (sortConfig.key) {
            filtered.sort((a, b) => {
                if (a[sortConfig.key] < b[sortConfig.key]) {
                    return sortConfig.direction === 'ascending' ? -1 : 1;
                }
                if (a[sortConfig.key] > b[sortConfig.key]) {
                    return sortConfig.direction === 'ascending' ? 1 : -1;
                }
                return 0;
            });
        }

        return filtered;
    }, [buyersData, filters, sortConfig]);

    const requestSort = (key) => {
        let direction = 'ascending';
        if (sortConfig.key === key && sortConfig.direction === 'ascending') {
            direction = 'descending';
        }
        setSortConfig({ key, direction });
    };

    const handleExport = () => {
        if (filteredAndSortedBuyers.length === 0) {
            alert("No data to export.");
            return;
        }
        setIsExporting(true);

        const dataToExport = filteredAndSortedBuyers.map(buyer => ({
            "Madad ID": buyer.madadId,
            "Trade Name": buyer.tradeName,
            "MSME Associated with Name": buyer.msmeAssociatedWithName,
            "Applicant Name (Buyer)": buyer.applicantName,
            "CR Number": buyer.crNumber,
            "Onboarding Status": buyer.onboardingStatus,
            "Date Onboarded": formatDate(buyer.dateOnboarded),
        }));

        try {
            const worksheet = XLSX.utils.json_to_sheet(dataToExport);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Buyer Report");

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            XLSX.writeFile(workbook, `Buyer_Onboarding_Report_${timestamp}.xlsx`);
        } catch (exportError) {
            console.error("Error exporting data to Excel:", exportError);
            alert("An error occurred during export. Please check the console.");
        } finally {
            setIsExporting(false);
        }
    };

    const getSortIndicator = (key) => {
        if (sortConfig.key !== key) return null;
        return sortConfig.direction === 'ascending' ? ' ▲' : ' ▼';
    };

    if (isLoading) {
        return <div className="flex justify-center items-center h-screen"><div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div></div>;
    }

    if (error) {
        return <div className="p-6 bg-red-50 text-red-800 rounded-lg max-w-2xl mx-auto mt-10">Error: {error}</div>;
    }

    return (
        <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                <div className="sm:flex sm:items-center sm:justify-between mb-6">
                    <Link href="/reports">
                        <button className="mb-4 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50">
                            ← Back to Reports
                        </button>
                    </Link>
                    <h1 className="text-2xl font-bold text-gray-900">Buyer Onboarding Report</h1>
                    <button
                        onClick={handleExport}
                        disabled={isExporting}
                        className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isExporting ? (
                            <>
                                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
                                </svg>
                                Exporting...
                            </>
                        ) : 'Export to Excel'}
                    </button>
                </div>

                <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <input
                            type="text"
                            name="searchTerm"
                            value={filters.searchTerm}
                            onChange={handleFilterChange}
                            placeholder="Search by ID, Name, CR..."
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                        <select
                            name="onboardingStatus"
                            value={filters.onboardingStatus}
                            onChange={handleFilterChange}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        >
                            <option value="All">All Statuses</option>
                            <option value="Onboarded">Onboarded</option>
                            <option value="Non-Onboarded">Non-Onboarded</option>
                        </select>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-100">
                                <tr>
                                    {/* Table Headers */}
                                    {[
                                        { key: 'madadId', label: 'Buyer ID' },
                                        { key: 'tradeName', label: 'Trade Name' },
                                        { key: 'msmeAssociatedWithName', label: 'MSME Associated with Name' },
                                        { key: 'applicantName', label: 'Applicant Name' },
                                        { key: 'crNumber', label: 'CR Number' },
                                        { key: 'onboardingStatus', label: 'Onboarding Status' },
                                        { key: 'dateOnboarded', label: 'Date Onboarded' },
                                    ].map(header => (
                                        <th key={header.key} scope="col" onClick={() => requestSort(header.key)} className="px-4 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-wider cursor-pointer select-none">
                                            {header.label}
                                            <span className="ml-1">{getSortIndicator(header.key)}</span>
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {filteredAndSortedBuyers.length > 0 ? (
                                    filteredAndSortedBuyers.map((buyer, index) => (
                                        <tr key={`${buyer.madadId}-${buyer.applicantName}-${index}`} className="hover:bg-gray-50">
                                            <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-800">{buyer.madadId || 'N/A'}</td>
                                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">{buyer.tradeName || 'N/A'}</td>
                                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">{buyer.msmeAssociatedWithName || 'N/A'}</td>
                                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">{buyer.applicantName || 'N/A'}</td>
                                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">{buyer.crNumber || 'N/A'}</td>
                                            <td className="px-4 py-4 whitespace-nowrap text-sm"><OnboardingStatusBadge status={buyer.onboardingStatus} /></td>
                                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">{formatDate(buyer.dateOnboarded)}</td>
                                        </tr>
                                    ))
                                ) : (
                                    <tr>
                                        <td colSpan="7" className="text-center py-10 text-gray-500">No buyers found for the selected filters.</td>
                                    </tr>
                                )}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
}