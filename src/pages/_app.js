import '@/styles/globals.css';
import { useAuth } from '@/contexts/authContext';
import Sidebar from '@/components/Sidebar';
import Header from '@/components/Header';
import { useRouter } from 'next/router';
import {AuthProvider} from "../contexts/authContext"

// Inner component that uses the auth context
function AppContent({ Component, pageProps }) {
  const router = useRouter();
  const { loading, isAuthenticated } = useAuth();
  const isLoginPage = router.pathname === '/';

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#6fb468]"></div>
      </div>
    );
  }

  // If it's login page, show without layout
  if (isLoginPage) {
    return <Component {...pageProps} />;
  }

  // If user is not authenticated and not on login page, redirect to login
  if (!isAuthenticated() && !isLoginPage) {
    router.push('/');
    return null;
  }
return (
  <div className="h-screen flex flex-col">
    <Header />
    <div className="flex flex-1 overflow-hidden">
      <Sidebar activePage={router.pathname} />
      <main className="flex-1 p-6 overflow-auto">
        <Component {...pageProps} />
      </main>
    </div>
  </div>
);


}

export default function MyApp({ Component, pageProps }) {
  return (
    <AuthProvider>
      <AppContent Component={Component} pageProps={pageProps} />
    </AuthProvider>
  );
}