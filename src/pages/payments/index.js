import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import * as XLSX from 'xlsx'; // SheetJS library for Excel
import { saveAs } from 'file-saver'; // For triggering file download
import { Disclosure } from '@headlessui/react';
import { FunnelIcon, ChevronUpIcon, XMarkIcon } from '@heroicons/react/24/outline';
import axios from 'axios';
import StatusBadge from '../../components/StatusBadge';
import { formatPhoneNumber } from '../../utils/phoneFormatter';
import Image from 'next/image';
import config from "../../../config.json"

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Invalid Date';
    return date.toLocaleDateString('en-CA'); // Format YYYY-MM-DD
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return 'Invalid Date';
  }
};

// --- DisbursalRepaymentFilterSection Component ---
const DisbursalRepaymentFilterSection = ({ filters, setFilters, resetFilters, uniqueOfferStatuses = [], uniqueInvoiceStatuses = [] }) => {
  // Input styling
  const inputBaseClass = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
  const numberInputClass = `${inputBaseClass} px-2 py-1`;
  const dateInputClass = `${inputBaseClass} px-2 py-1`;
  const textInputClass = `${inputBaseClass} px-3 py-1.5`;
  const selectInputClass = `${inputBaseClass} px-3 py-2`;

  const handleFilterChangeInternal = (event) => {
    const { name, value } = event.target;
    setFilters(prevFilters => ({ ...prevFilters, [name]: value }));
  };

  const activeFilterCount = useMemo(() => {
    return Object.values(filters).filter(v => v !== '' && v !== null && v !== undefined).length;
  }, [filters]);

  const handleResetInternal = (event) => {
    event.stopPropagation();
    resetFilters();
  };

  return (
    <div className="mb-6">
      <Disclosure as="div" className="border border-gray-200 rounded-lg shadow-sm bg-white">
        {({ open }) => (
          <>
            {/* Header Bar */}
            <div className="flow-root">
              <Disclosure.Button className="flex w-full items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none">
                <span className="flex items-center">
                  <FunnelIcon className="mr-2 h-5 w-5 text-gray-400" aria-hidden="true" />
                  Filters
                  {activeFilterCount > 0 && (
                    <span className="ml-2 rounded-full bg-gray-200 px-2 py-0.5 text-xs font-medium text-gray-800">
                      {activeFilterCount}
                    </span>
                  )}
                </span>
                <span className="ml-6 flex items-center">
                  <ChevronUpIcon className={`${open ? 'rotate-180' : ''} h-5 w-5 text-gray-500 transition-transform`} />
                </span>
              </Disclosure.Button>
            </div>

            {/* Separator */}
            {open && <div className="border-t border-gray-200"></div>}

            {/* Panel */}
            <Disclosure.Panel className="px-4 py-5 sm:px-6 lg:px-8">
              {/* Top Row: Search & Clear */}
              <div className="mb-6 flex items-start justify-between gap-4">
                <div className="flex-1">
                  <label htmlFor="mainSearchText" className="sr-only">Search</label>
                  <input
                    type="text" name="searchText" id="mainSearchText"
                    value={filters.searchText} onChange={handleFilterChangeInternal}
                    className={textInputClass} placeholder="Search Offer ID, Merchant, Invoice..."
                  />
                </div>
                <button
                  type="button" onClick={handleResetInternal}
                  className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                >
                  <XMarkIcon className="-ml-1 mr-1.5 h-4 w-4 text-gray-400" /> Clear all
                </button>
              </div>

              {/* Filter Grid - Adjust columns as needed */}
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">

                {/* Offer Amount Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Offer Amount (QAR)</label>
                  <div className="flex space-x-2">
                    <input type="number" name="minAmount" value={filters.minAmount} onChange={handleFilterChangeInternal} placeholder="Min" className={numberInputClass} />
                    <input type="number" name="maxAmount" value={filters.maxAmount} onChange={handleFilterChangeInternal} placeholder="Max" className={numberInputClass} />
                  </div>
                </div>

                {/* Offer Status Dropdown */}
                <div>
                  <label htmlFor="offerStatusFilter" className="block text-sm font-medium text-gray-700 mb-1">Offer Status</label>
                  <select id="offerStatusFilter" name="offerStatus" value={filters.offerStatus} onChange={handleFilterChangeInternal} className={selectInputClass}>
                    <option value="">All Offers</option>
                    {uniqueOfferStatuses.map(status => <option key={status} value={status}>{status}</option>)}
                  </select>
                </div>

                {/* Invoice Status Dropdown */}
                <div>
                  <label htmlFor="invoiceStatusFilter" className="block text-sm font-medium text-gray-700 mb-1">Invoice Status</label>
                  <select id="invoiceStatusFilter" name="invoiceStatus" value={filters.invoiceStatus} onChange={handleFilterChangeInternal} className={selectInputClass}>
                    <option value="">All Invoices</option>
                    {uniqueInvoiceStatuses.map(status => <option key={status} value={status}>{status}</option>)}
                  </select>
                </div>

                {/* Disbursal Date Range */}
                <div className="sm:col-span-2 md:col-span-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Disbursal Date</label>
                  <div className="flex flex-col space-y-2">
                    <input type="date" name="disbursalDateFrom" value={filters.disbursalDateFrom} onChange={handleFilterChangeInternal} className={dateInputClass} aria-label="Disbursal Start Date" />
                    <input type="date" name="disbursalDateTo" value={filters.disbursalDateTo} onChange={handleFilterChangeInternal} className={dateInputClass} aria-label="Disbursal End Date" />
                  </div>
                </div>

                {/* Next Due Date Range */}
                <div className="sm:col-span-2 md:col-span-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Next Due Date</label>
                  <div className="flex flex-col space-y-2">
                    <input type="date" name="nextDueDateFrom" value={filters.nextDueDateFrom} onChange={handleFilterChangeInternal} className={dateInputClass} aria-label="Next Due Start Date" />
                    <input type="date" name="nextDueDateTo" value={filters.nextDueDateTo} onChange={handleFilterChangeInternal} className={dateInputClass} aria-label="Next Due End Date" />
                  </div>
                </div>

              </div>
            </Disclosure.Panel>
          </>
        )}
      </Disclosure>
    </div>
  );
};

const formatCurrency = (value, currency = 'QAR') => {
  const numValue = (typeof value === 'string') ? parseFloat(value.replace(/,/g, '')) : value;
  if (numValue === null || numValue === undefined || isNaN(numValue)) return 'N/A';
  return `${currency} ${Number(numValue).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

// Using the shared StatusBadge component from src/components/StatusBadge.js

// --- Info Icon Component ---
const InfoIcon = ({ tooltipText }) => {
  const [showTooltip, setShowTooltip] = useState(false);
  return (
    <div className="relative inline-flex items-center ml-2">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5 text-gray-400 hover:text-blue-500 cursor-pointer"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        strokeWidth={2}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        <path strokeLinecap="round" strokeLinejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      {showTooltip && (
        <div className="absolute z-20 bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs font-medium text-white bg-gray-900 rounded-md shadow-lg w-72 whitespace-normal"
          style={{ pointerEvents: 'none' }}>
          {tooltipText}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-x-4 border-x-transparent border-t-4 border-t-gray-900"></div> {/* Arrow */}
        </div>
      )}
    </div>
  );
};

// --- Main Component ---
export default function DisbursalRepaymentPage() {
  const [allOffersData, setAllOffersData] = useState([]);
  const [lenderDetailsMap, setLenderDetailsMap] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeSection, setActiveSection] = useState('disbursal');
  const [showEmiModal, setShowEmiModal] = useState(false);
  const [selectedOfferForModal, setSelectedOfferForModal] = useState(null);
  const [feedbackMessage, setFeedbackMessage] = useState({ type: '', text: '' });
  const [lenderId, setLenderId] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const disbursalFileRef = useRef(null);
  const repaymentFileRef = useRef(null);

  // --- Commercial State for Filters ---
  const [filters, setFilters] = useState({
    // Common filters
    searchText: '', // For Offer ID, Merchant Name/ID, Invoice #
    offerStatus: '', // Single select
    minAmount: '',   // Filter by Offer Amount (e.g., creditLimit)
    maxAmount: '',   // Filter by Offer Amount

    // Disbursal specific (but keep in main state for simplicity)
    invoiceStatus: '', // Single select
    disbursalDateFrom: '',
    disbursalDateTo: '',

    // Repayment specific (but keep in main state)
    nextDueDateFrom: '',
    nextDueDateTo: '',
  });

  // Combined Filter Change Handler
  // const handleFilterChange = (event) => {
  //   const { name, value } = event.target;
  //   setFilters(prevFilters => ({
  //     ...prevFilters,
  //     [name]: value
  //   }));
  // };

  // Reset Function for all filters
  const resetFilters = useCallback(() => {
    if (isUploading) return; // Prevent reset while uploading
    setFilters({
      searchText: '',
      offerStatus: '',
      minAmount: '',
      maxAmount: '',
      invoiceStatus: '',
      disbursalDateFrom: '',
      disbursalDateTo: '',
      nextDueDateFrom: '',
      nextDueDateTo: '',
    });
  }, [isUploading]); // No dependencies needed

  const uniqueOfferStatuses = useMemo(() => {
    const statuses = new Set(allOffersData.map(item => item.offerInfo?.status).filter(Boolean));
    return [...statuses].sort();
  }, [allOffersData]); // DEPEND ON allOffersData

  const uniqueInvoiceStatuses = useMemo(() => {
    const statuses = new Set(allOffersData.map(item => item.invoiceInfo?.invoiceStatus).filter(status => status && status !== 'N/A'));
    return [...statuses].sort();
  }, [allOffersData]); // DEPEND ON allOffersData

  useEffect(() => {
    const fetchAllLenderDetails = async () => {
      let tempError = null; // Temporary error storage
      try {
        console.log("Attempting to fetch all lender details..."); // Added log
        // Ensure this endpoint '/ops/invoiceFinancing/lender-admins' returns the JSON structure you provided
        const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/lender-admins`);

        console.log("Lender details API Response:", response); // Log the full response

        // --- *** CORRECTED PART *** ---
        // Check if response.data exists AND if response.data.data is the array
        if (response.data && Array.isArray(response.data.data)) {
          // Use response.data.data instead of response.data.lenders
          const lendersArray = response.data.data;
          const map = lendersArray.reduce((acc, lender) => {
            if (lender && lender._id) { // Check if lender object and _id exist
              acc[lender._id] = {
                lenderName: lender.lenderName || 'Unnamed Lender', // Fallback name
                logoUrl: lender.logoUrl || null
              };
            } else {
              console.warn("Skipping lender object due to missing _id:", lender);
            }
            return acc;
          }, {});

          if (Object.keys(map).length === 0 && lendersArray.length > 0) {
            console.warn("Lender details map is empty, but API returned data. Check IDs/structure.");
          } else if (Object.keys(map).length > 0) {
            console.log("Lender details map successfully created:", map);
          } else {
            console.log("Lender details map is empty as API returned no valid lender data.");
          }
          setLenderDetailsMap(map);

        } else {
          // Log a more specific warning if the structure is unexpected
          console.warn("Could not fetch or parse lender details. Expected 'response.data.data' to be an array. Received:", response.data);
          tempError = "Failed to parse lender details from API response.";
          setLenderDetailsMap({}); // Set empty map on structure mismatch
        }
        // --- *** END OF CORRECTION *** ---

      } catch (err) {
        console.error('Error fetching lender details:', err);
        // Construct a more informative error message
        const errorMessage = `Failed to load lender details: ${err.response?.data?.message || err.message || 'Unknown error'}`;
        console.error(errorMessage);
        tempError = errorMessage; // Store error message
        setLenderDetailsMap({}); // Set empty map on error
      } finally {
        // Update the main error state *after* the try-catch block
        // This avoids potential state update issues if fetchData runs concurrently
        if (tempError) {
          setError(prev => prev ? `${prev}\n${tempError}` : tempError);
        }
      }
    };

    fetchAllLenderDetails();
  }, []); // 

  // Effect to load lenderId from localStorage
  useEffect(() => {
    const storedLenderId = localStorage.getItem('userId');
    if (storedLenderId) {
      setLenderId(storedLenderId);
    } else {
      console.error("Lender ID not found in localStorage. Cannot fetch data.");
      setError("Lender ID not found. Please ensure you are logged in.");
      setLoading(false);
    }
  }, []);

  // Fetch Data Function - MODIFIED for Admin API
  const fetchData = useCallback(async () => {
    // Note: isUploading check remains relevant if uploads disable fetching
    if (isUploading) return;
    try {
      setLoading(true);
      setError(null); // Clear previous errors on refetch

      // --- Use the NEW Admin API Endpoint ---
      const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/admin/all-lender-repayment-disbursal`); // Use the new route

      // No need to check for lenderId in localStorage anymore for this fetch

      if (!response || !response.data || !response.data.success) {
        throw new Error(response.data?.message || 'Failed to fetch admin data');
      }

      const results = response.data.data; // This is the array: [{ lenderId: '...', data: [...] }, ...]

      // --- Flatten the data ---
      const flattenedData = results.reduce((acc, lenderGroup) => {
        const lenderId = lenderGroup.lenderId;
        if (lenderGroup.data && Array.isArray(lenderGroup.data)) {
          // Add lenderId to each item within the lender's data array
          const itemsWithLenderId = lenderGroup.data.map(item => ({
            ...item,
            lenderId: lenderId, // Add the lenderId here
            // Generate a unique client-side ID if needed for keys, combining lenderId and offerId
            clientId: `${lenderId}-${item.offerInfo.offerId}`
          }));
          acc.push(...itemsWithLenderId);
        }
        return acc;
      }, []);

      console.log("Flattened data:", flattenedData);
      setAllOffersData(flattenedData); // Set the flattened data to state

    } catch (error) {
      console.error('Error fetching admin repayment/disbursal data:', error);
      setError(error.message || "An unknown error occurred while fetching data.");
    } finally {
      setLoading(false);
    }
    // }, [isUploading]); // Removed fetchData dependency as it now calls itself
  }, [isUploading]); // Keep isUploading dependency


  const isValidObjectId = (id) => /^[0-9a-fA-F]{24}$/.test(id);

  // --- Complete Disbursal Upload Handler ---
  const handleDisbursalUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;
    setIsUploading(true);
    setFeedbackMessage({ type: '', text: '' });
    let currentFeedback = { type: 'info', text: 'Reading and validating disbursal file...' };
    setFeedbackMessage(currentFeedback);

    const reader = new FileReader();
    reader.onload = async (e) => {
      const validationErrors = [];
      const payload = [];

      try {
        // Start: File Reading and Basic Checks
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array', cellDates: true });
        const sheetName = workbook.SheetNames[0];
        if (!sheetName) throw new Error("Cannot find sheet in Excel file.");
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: null, raw: false });

        if (!jsonData || jsonData.length < 2) throw new Error("Excel file appears empty or lacks a header row.");

        const header = jsonData[0].map(h => String(h || '').trim());
        const expectedHeader = ["UTR", "invoiceOfferId", "disbursementTimestamp", "disbursalAmount"];
        if (JSON.stringify(header) !== JSON.stringify(expectedHeader)) {
          throw new Error(`Invalid header row. Expected columns: ${expectedHeader.join(', ')}`);
        }
        // End: File Reading

        // Start: Detailed Row Validation
        // Start: Detailed Row Validation
        const dataRows = jsonData.slice(1);

        dataRows.forEach((row, index) => {
          const rowNum = index + 2;
          // Extract data from Excel row
          const offerIdFromFile = row?.[0];
          const excelEmiNumberInput = row?.[1];
          const utrFromFile = row?.[2];
          const timestampInputFromFile = row?.[3];
          const paidAmountRawValue = row?.[4]; // Get the raw value for paidAmount

          // Log the raw values from the row
          console.log(
            `Row ${rowNum} Raw Data: offerId=${offerIdFromFile}, emiNum=${excelEmiNumberInput}, utr=${utrFromFile}, tsInput=${timestampInputFromFile}, paidAmountRaw=${paidAmountRawValue} (type: ${typeof paidAmountRawValue})`
          );

          // --- Initialize Row Validation ---
          let rowIsValid = true;
          let rowErrors = [];
          let parsedTimestamp = null;
          let parsedExcelEmiNum = NaN;
          let parsedPaidAmt = NaN; // This will be set by the CORRECT logic below
          let targetEmiNumber = null;

          // --- Basic Row Validations ---

          // Validate Offer ID matches the one selected in the modal
          if (!offerIdFromFile || !isValidObjectId(String(offerIdFromFile).trim()) || String(offerIdFromFile).trim() !== selectedOfferForModal.offerInfo.offerId) {
            rowErrors.push(`Invalid/Missing offerId or doesn't match selected offer (${selectedOfferForModal.offerInfo.offerId})`);
            rowIsValid = false;
          }

          // Validate EMI Number from Excel
          parsedExcelEmiNum = parseInt(excelEmiNumberInput, 10);
          if (isNaN(parsedExcelEmiNum) || parsedExcelEmiNum <= 0) {
            rowErrors.push("Invalid/Missing positive emiNumber in Excel file");
            rowIsValid = false;
          }

          // Validate UTR is present
          if (!utrFromFile || String(utrFromFile).trim() === '') {
            rowErrors.push("Missing UTR");
            rowIsValid = false;
          }

          // Validate Timestamp
          if (timestampInputFromFile === null || timestampInputFromFile === undefined || String(timestampInputFromFile).trim() === '') {
            rowErrors.push("Missing paymentTimestamp");
            rowIsValid = false;
          } else {
            let date = new Date(timestampInputFromFile);
            if (isNaN(date.getTime()) && typeof timestampInputFromFile === 'number' && timestampInputFromFile > 25569) { // Excel date number check
              const excelEpoch = new Date(1899, 11, 30);
              const jsTimestamp = excelEpoch.getTime() + (timestampInputFromFile * 24 * 60 * 60 * 1000);
              date = new Date(jsTimestamp);
            }
            if (isNaN(date.getTime())) {
              rowErrors.push("Invalid paymentTimestamp format (use YYYY-MM-DD, MM/DD/YYYY, or Excel date number)");
              rowIsValid = false;
            } else {
              parsedTimestamp = date.toISOString();
            }
          }

          // ---- THIS IS THE ONLY PAID AMOUNT PARSING LOGIC THAT SHOULD EXIST ----
          if (paidAmountRawValue === null || paidAmountRawValue === undefined || String(paidAmountRawValue).trim() === '') {
            rowErrors.push("Missing paidAmount");
            rowIsValid = false;
          } else {
            const paidAmountStr = String(paidAmountRawValue);
            console.log(`Row ${rowNum} - paidAmountRawValue (as string): "${paidAmountStr}"`);

            // Remove any character that is NOT a digit or a decimal point.
            const cleanedAmountString = paidAmountStr.replace(/[^\d.]/g, '');
            console.log(`Row ${rowNum} - cleanedAmountString: "${cleanedAmountString}"`);

            if (cleanedAmountString === '' || cleanedAmountString === '.') { // Also check for just a decimal point
              rowErrors.push("PaidAmount became empty or invalid after cleaning non-numeric characters.");
              rowIsValid = false;
            } else {
              parsedPaidAmt = parseFloat(cleanedAmountString);
              console.log(`Row ${rowNum} - final parsedPaidAmt: ${parsedPaidAmt}`);

              if (isNaN(parsedPaidAmt) || parsedPaidAmt < 0) {
                rowErrors.push("Invalid or missing non-negative paidAmount (ensure it's a number after cleaning)");
                rowIsValid = false;
              }
            }
          }
          // ---- END OF PAID AMOUNT PARSING LOGIC ----

          // --- FIFO Logic Implementation ---
          if (rowIsValid) {
            const firstPendingEmi = sortedCurrentEmiDetails.find(emi => emi.rePaymentStatus === 'PENDING');
            if (!firstPendingEmi) {
              rowErrors.push("Cannot apply payment: No pending EMIs found for this offer.");
              rowIsValid = false;
            } else {
              targetEmiNumber = firstPendingEmi.emiNumber;
              if (targetEmiNumber !== parsedExcelEmiNum) {
                console.log(`FIFO Applied: Row ${rowNum} (Excel EMI# ${parsedExcelEmiNum}) payment is being applied to first pending EMI #${targetEmiNumber}`);
              }
            }
          }

          // --- Add to Payload if Valid ---
          if (rowIsValid && targetEmiNumber !== null) {
            payload.push({
              offerId: String(offerIdFromFile).trim(),
              emiNumber: targetEmiNumber,
              utr: String(utrFromFile).trim(),
              paymentTimestamp: parsedTimestamp,
              paidAmount: parsedPaidAmt // This 'parsedPaidAmt' should now be correct
            });
          } else {
            if (rowErrors.length > 0) {
              validationErrors.push({ row: rowNum, errors: rowErrors.join('; ') });
            }
          }
        }); // --- End of dataRows.forEach ---

        if (payload.length === 0) {
          let errorMsg = `No valid data rows found to process.`;
          if (validationErrors.length > 0) {
            const clientErrors = validationErrors.map(err => `(Row ${err.row}) ${err.errors}`).slice(0, 5);
            errorMsg += ` Validation Errors Found: ${clientErrors.join('; ')}`;
            if (validationErrors.length > 5) errorMsg += "; ... (more errors exist)";
          }
          throw new Error(errorMsg);
        }

        currentFeedback = { type: 'info', text: `Client validation complete. Sending ${payload.length} valid record(s)...` };
        setFeedbackMessage(currentFeedback);

        // Start: API Calls and Subsequent Processing
        const offerUpdateResponse = await fetch(`${config.apiUrl}/ops/invoiceFinancing/offers/update-disbursal-status`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        });
        const offerUpdateResult = await offerUpdateResponse.json();

        if (!offerUpdateResponse.ok || !offerUpdateResult.success) {
          const combinedErrors = [
            ...validationErrors,
            ...(offerUpdateResult.errors || []).map(apiErr => ({
              row: apiErr.recordIndex ?? 'N/A',
              errors: apiErr.message || apiErr.error || 'Unknown API error on this record'
            }))
          ];
          const errorMsg = offerUpdateResult.message || `Offer Update API Error (Status: ${offerUpdateResponse.status})`;
          throw new Error(errorMsg, { cause: combinedErrors });
        }

        let initialSuccessMsg = offerUpdateResult.message || `${offerUpdateResult.updatedCount || payload.length} offer(s) processed.`;
        if (validationErrors.length > 0) initialSuccessMsg += ` (${validationErrors.length} initial row validation errors detected).`;

        currentFeedback = { type: 'info', text: `${initialSuccessMsg} Updating related invoice statuses...` };
        setFeedbackMessage(currentFeedback);

        let invoiceUpdateSuccessCount = 0;
        const invoiceUpdateErrors = [];
        const successfullyUpdatedOfferIds = offerUpdateResult.successDetails?.map(detail => detail.invoiceOfferId) ?? payload.map(p => p.invoiceOfferId);

        await Promise.all(payload
          .filter(p => successfullyUpdatedOfferIds.includes(p.invoiceOfferId))
          .map(async (processedOffer) => {
            const offerId = processedOffer.invoiceOfferId;
            const originalOfferData = allOffersData.find(item => item.offerInfo.offerId === offerId);

            if (!originalOfferData || !originalOfferData.invoiceInfo?.invoiceId) {
              console.warn(`Could not find invoiceId for processed offerId: ${offerId}. Skipping invoice update.`);
              invoiceUpdateErrors.push({ offerId: offerId, message: "Invoice ID not found." });
              return;
            }
            const invoiceIdToUpdate = originalOfferData.invoiceInfo.invoiceId;
            try {
              const invoiceUpdateResponse = await fetch(`${config.apiUrl}/ops/invoiceFinancing/updateInvoice/${invoiceIdToUpdate}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ status: 'DISBURSED' })
              });
              if (!invoiceUpdateResponse.ok) {
                const errorData = await invoiceUpdateResponse.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP ${invoiceUpdateResponse.status}`);
              }
              invoiceUpdateSuccessCount++;
            } catch (invoiceErr) {
              console.error(`Error updating invoice status for invoice ${invoiceIdToUpdate} (Offer ${offerId}):`, invoiceErr);
              invoiceUpdateErrors.push({ offerId: offerId, invoiceId: invoiceIdToUpdate, message: invoiceErr.message });
            }
          })
        );
        // End: API Calls

        // Final Feedback
        let finalMsg = initialSuccessMsg;
        if (successfullyUpdatedOfferIds.length > 0) {
          if (invoiceUpdateErrors.length === 0) {
            finalMsg += ` All ${invoiceUpdateSuccessCount} corresponding invoice statuses updated.`;
            currentFeedback = { type: 'success', text: finalMsg };
          } else {
            finalMsg += ` Updated ${invoiceUpdateSuccessCount} invoice statuses.`;
            let errorSummary = ` Failed to update ${invoiceUpdateErrors.length}.`;
            let errorDetails = invoiceUpdateErrors.map(e => `(Offer ${e.offerId}/Inv ${e.invoiceId || 'N/A'}) ${e.message}`).slice(0, 3).join('; ');
            if (invoiceUpdateErrors.length > 3) errorDetails += "; ... (see console)";
            finalMsg += `${errorSummary} Details: ${errorDetails}`;
            currentFeedback = { type: 'warning', text: finalMsg };
          }
        } else {
          // If no offers were successfully updated initially
          currentFeedback = { type: validationErrors.length > 0 ? 'warning' : 'success', text: initialSuccessMsg };
        }
        setFeedbackMessage(currentFeedback);
        fetchData();

      } catch (err) { // --- Robust Catch Block ---
        console.error("[Upload Error] handleDisbursalUpload:", err);
        let userMessage = "Disbursal upload failed. ";

        try {
          let mainReason = err?.message || "An unknown error occurred.";
          let specificDetails = [];
          let isStructuredError = (err?.cause && Array.isArray(err.cause)) || mainReason.includes("Validation Errors Found:");
          console.log(isStructuredError);

          if (err?.cause && Array.isArray(err.cause)) {
            mainReason = err.message || "API or Validation errors found.";
            specificDetails = err.cause.map(e => `(Row ${e?.row ?? 'N/A'}) ${e?.errors || e?.message || e?.error || 'Details unavailable'}`).slice(0, 5);
            if (err.cause.length > 5) specificDetails.push("... (more in console)");
          }
          else if (Array.isArray(validationErrors) && validationErrors.length > 0) {
            mainReason = mainReason.includes("Validation Errors") ? mainReason : "File validation failed.";
            specificDetails = validationErrors.map(e => `(Row ${e?.row ?? 'N/A'}) ${e?.errors || 'Details unavailable'}`).slice(0, 5);
            if (validationErrors.length > 5) specificDetails.push("... (more errors in file)");
          }

          userMessage = `Upload Failed: ${mainReason}`;
          if (specificDetails.length > 0) { userMessage += ` Details: ${specificDetails.join('; ')}`; }

          if (mainReason.toLowerCase().includes("invalid header")) { userMessage += " Check template column names."; }
          else if (mainReason.includes("No valid data rows")) { userMessage += " Check file data format."; }
          else if (!navigator.onLine) { userMessage += " Check internet connection."; }

        } catch (formattingError) {
          console.error("[Upload Error] Formatting Error:", formattingError);
          userMessage = `Upload failed: ${err?.message || 'An critical unknown error occurred.'}`;
        }
        setFeedbackMessage({ type: 'error', text: userMessage });

      } finally {
        setIsUploading(false);
        try { if (disbursalFileRef?.current) disbursalFileRef.current.value = ""; }
        catch (e) { console.error("Error resetting disbursal file input:", e); }
      }
    }; // end reader.onload

    reader.onerror = (err) => {
      console.error("[Upload Error] File Reading Error:", err);
      setFeedbackMessage({ type: 'error', text: `Error reading file: ${err?.message || 'Could not read the selected file.'}` });
      setIsUploading(false);
      try { if (disbursalFileRef?.current) disbursalFileRef.current.value = ""; }
      catch (e) { console.error("Error resetting disbursal file input:", e); }
    };
    reader.readAsArrayBuffer(file);
  }; // --- END handleDisbursalUpload ---

  const handleRepaymentUpload = async (event) => {
    const file = event.target.files[0];
    if (!file || !selectedOfferForModal) {
      setFeedbackMessage({ type: 'warning', text: 'Please select an offer first (click "Update EMIs") and then choose a file.' });
      // Clear the file input if an offer wasn't selected
      if (repaymentFileRef.current) repaymentFileRef.current.value = "";
      return;
    }
    setIsUploading(true);
    setFeedbackMessage({ type: 'info', text: 'Reading and validating repayment file...' });

    const reader = new FileReader();
    reader.onload = async (e) => {
      const validationErrors = [];
      const payload = [];

      try {
        // --- Start: Get and Sort Current EMIs (Place this BEFORE the forEach loop) ---
        const currentEmiDetails = selectedOfferForModal?.offerInfo?.emiDetails;
        if (!Array.isArray(currentEmiDetails)) {
          throw new Error(`EMI details not found or invalid for selected offer ${selectedOfferForModal?.offerInfo?.offerId}`);
        }
        // Sort by EMI number to ensure correct FIFO order checking
        const sortedCurrentEmiDetails = [...currentEmiDetails].sort((a, b) => (a.emiNumber || 0) - (b.emiNumber || 0));
        console.log(sortedCurrentEmiDetails);
        // --- End: Get and Sort Current EMIs ---
        // Start: File Reading and Basic Checks
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array', cellDates: true });
        const sheetName = workbook.SheetNames[0];
        if (!sheetName) throw new Error("Cannot find sheet in Excel file.");
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: null, raw: false });

        if (!jsonData || jsonData.length < 2) throw new Error("Excel file appears empty or lacks a header row.");

        const header = jsonData[0].map(h => String(h || '').trim());
        const expectedHeader = ["offerId", "emiNumber", "UTR", "paymentTimestamp", "paidAmount"];
        if (JSON.stringify(header) !== JSON.stringify(expectedHeader)) {
          throw new Error(`Invalid header row. Expected columns: ${expectedHeader.join(', ')}`);
        }
        // End: File Reading

        // Start: Detailed Row Validation
        const dataRows = jsonData.slice(1);

        // --- Modified forEach loop ---
        dataRows.forEach((row, index) => {
          const rowNum = index + 2;
          // Extract data from Excel row
          const offerId = row?.[0];
          const excelEmiNumberInput = row?.[1]; // EMI number specified in the file
          const utr = row?.[2];
          const timestampInput = row?.[3];
          const paidAmountInput = row?.[4]; // Get the raw input from the Excel row

          // --- Initialize Row Validation ---
          let rowIsValid = true;
          let rowErrors = [];
          let parsedTimestamp = null;
          let parsedExcelEmiNum = NaN; // Validate the number from Excel, though we might override it
          let parsedPaidAmt = NaN;
          let targetEmiNumber = null; // This will hold the actual EMI number to update (FIFO target)

          // --- Basic Row Validations (Offer ID, UTR, Timestamp, Amount) ---
          // Validate Offer ID matches the one selected in the modal
          if (!offerId || !isValidObjectId(String(offerId).trim()) || String(offerId).trim() !== selectedOfferForModal.offerInfo.offerId) {
            rowErrors.push(`Invalid/Missing offerId or doesn't match selected offer (${selectedOfferForModal.offerInfo.offerId})`);
            rowIsValid = false;
          }

          // Validate EMI Number from Excel - check if it's a valid number format, even if unused by FIFO later
          parsedExcelEmiNum = parseInt(excelEmiNumberInput, 10);
          if (isNaN(parsedExcelEmiNum) || parsedExcelEmiNum <= 0) {
            rowErrors.push("Invalid/Missing positive emiNumber in Excel file");
            // Decide if this is a hard fail: Let's make it a hard fail. Even with FIFO, the input should be sensible.
            rowIsValid = false;
          }

          // Validate UTR is present
          if (!utr || String(utr).trim() === '') {
            rowErrors.push("Missing UTR");
            rowIsValid = false;
          }

          // Validate Timestamp (using existing robust logic)
          if (timestampInput === null || timestampInput === undefined || String(timestampInput).trim() === '') {
            rowErrors.push("Missing paymentTimestamp");
            rowIsValid = false;
          } else {
            let date = new Date(timestampInput);
            if (isNaN(date.getTime()) && typeof timestampInput === 'number' && timestampInput > 25569) { // Excel date number check
              const excelEpoch = new Date(1899, 11, 30);
              const jsTimestamp = excelEpoch.getTime() + (timestampInput * 24 * 60 * 60 * 1000);
              date = new Date(jsTimestamp);
            }
            if (isNaN(date.getTime())) { // Check final parsed date
              rowErrors.push("Invalid paymentTimestamp format (use yyyy-MM-DD, MM/DD/YYYY, or Excel date number)");
              rowIsValid = false;
            } else {
              parsedTimestamp = date.toISOString(); // Store valid date as ISO string
            }
          }

          // Validate Paid Amount is a non-negative number
          // FIXED: Consistent handling of comma-separated numbers and better error reporting
          if (paidAmountInput === null || paidAmountInput === undefined || String(paidAmountInput).trim() === '') {
            rowErrors.push("Missing paidAmount");
            rowIsValid = false;
          } else {
            try {
              // 1. Convert to string (to handle cases where it might be read as a number by XLSX)
              // 2. Remove all commas (thousand separators)
              // 3. Fix any formatting issues (like multiple dots) by normalizing the decimal point
              const inputAsString = String(paidAmountInput);

              // Log the raw value for debugging
              console.log(`Row ${rowNum} raw amount: "${inputAsString}"`);

              // First clean up any invalid characters and normalize format
              // Replace multiple dots with a single dot and remove any non-numeric, non-dot, non-comma characters
              const normalizedString = inputAsString
                .replace(/\.+/g, '.') // Replace multiple dots with a single dot
                .replace(/[^\d.,]/g, ''); // Remove any non-numeric, non-dot, non-comma characters

              // Then remove commas after normalizing
              const cleanedAmountString = normalizedString.replace(/,/g, '');

              // Log the cleaned value for debugging
              console.log(`Row ${rowNum} cleaned amount: "${cleanedAmountString}"`);

              // Parse the final cleaned string
              parsedPaidAmt = parseFloat(cleanedAmountString);

              if (isNaN(parsedPaidAmt)) {
                rowErrors.push(`Invalid paidAmount format (received: "${inputAsString}")`);
                rowIsValid = false;
              } else if (parsedPaidAmt < 0) {
                rowErrors.push("paidAmount cannot be negative");
                rowIsValid = false;
              }
            } catch (numError) {
              console.error(`Row ${rowNum} amount parsing error:`, numError);
              rowErrors.push(`Failed to parse paidAmount (received: "${paidAmountInput}")`);
              rowIsValid = false;
            }
          }
          // --- End Basic Row Validations ---

          // --- FIFO Logic Implementation ---
          if (rowIsValid) { // Only apply FIFO if the row data itself is fundamentally valid
            // Find the first EMI in the sorted list that is 'PENDING'
            const firstPendingEmi = sortedCurrentEmiDetails.find(emi => emi.rePaymentStatus === 'PENDING');

            if (!firstPendingEmi) {
              // If no EMIs are pending, this payment cannot be applied using FIFO.
              rowErrors.push("Cannot apply payment: No pending EMIs found for this offer.");
              rowIsValid = false; // Mark row as invalid for payload
            } else {
              // FIFO Success: Target the first pending EMI found
              targetEmiNumber = firstPendingEmi.emiNumber;
              // Optional: Log the override for debugging/audit
              if (targetEmiNumber !== parsedExcelEmiNum) {
                console.log(`FIFO Applied: Row ${rowNum} (Excel EMI# ${parsedExcelEmiNum}) payment is being applied to first pending EMI #${targetEmiNumber}`);
              }
            }
          }
          // --- End FIFO Logic ---

          // --- Add to Payload if Valid ---
          if (rowIsValid && targetEmiNumber !== null) {
            // Push the data intended for the *first pending* EMI
            payload.push({
              offerId: String(offerId).trim(),
              emiNumber: targetEmiNumber, // <<< CRITICAL: Use the target number from FIFO logic
              utr: String(utr).trim(),
              paymentTimestamp: parsedTimestamp,
              paidAmount: parsedPaidAmt // This now correctly contains the cleaned and parsed amount
            });

            // Log the final amount being sent to the API
            console.log(`Row ${rowNum} final amount in payload: ${parsedPaidAmt}`);
          } else {
            // If the row was invalid at any point (basic validation or no pending EMI found)
            if (rowErrors.length > 0) {
              validationErrors.push({ row: rowNum, errors: rowErrors.join('; ') });
            }
            // No need for else-if here, if rowErrors has items, it's handled.
          }
          // --- End Payload Creation ---

        }); // --- End of dataRows.forEach ---
        // End: Detailed Row Validation

        if (payload.length === 0) {
          let errorMsg = `No valid data rows found for offer ${selectedOfferForModal.offerInfo.offerId}.`;
          if (validationErrors.length > 0) {
            const clientErrors = validationErrors.map(err => `(Row ${err.row}) ${err.errors}`).slice(0, 5);
            errorMsg += ` Validation Errors Found: ${clientErrors.join('; ')}`;
            if (validationErrors.length > 5) errorMsg += "; ... (more errors exist)";
          }
          throw new Error(errorMsg);
        }

        setFeedbackMessage({ type: 'info', text: `Validation complete. Sending ${payload.length} valid record(s) to server...` });

        // Start: API Call
        const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/offers/update-emi-repayments`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        });
        const result = await response.json();

        if (!response.ok || !result.success) {
          const combinedErrors = [
            ...validationErrors,
            ...(result.errors || []).map(apiErr => ({
              row: apiErr.recordIndex ?? 'N/A',
              errors: apiErr.message || apiErr.error || 'Unknown API error on this record'
            }))
          ];
          const errorMsg = result.message || `Repayment Update API Error (Status: ${response.status})`;
          throw new Error(errorMsg, { cause: combinedErrors });
        }
        // End: API Call

        // Success Feedback
        let successMsg = result.message || `${result.updatedEmiCount || payload.length} EMIs processed successfully for offer ${selectedOfferForModal.offerInfo.offerId}.`;
        if (validationErrors.length > 0) successMsg += ` (${validationErrors.length} row(s) had validation errors before sending).`;
        setFeedbackMessage({ type: 'success', text: successMsg });
        closeEmiModal();
        fetchData();

      } catch (err) { // --- Enhanced & Safer Catch Block ---
        console.error("[Upload Error] handleRepaymentUpload:", err);
        let userMessage = "Repayment upload failed. ";

        try {
          let mainReason = err?.message || "An unknown error occurred.";
          let specificDetails = [];
          let isStructuredError = (err?.cause && Array.isArray(err.cause)) || mainReason.includes("Validation Errors Found:");
          console.log(isStructuredError);

          if (err?.cause && Array.isArray(err.cause)) {
            mainReason = err.message || "API or Validation errors found.";
            specificDetails = err.cause.map(e => `(Row ${e?.row ?? 'N/A'}) ${e?.errors || e?.message || e?.error || 'Details unavailable'}`).slice(0, 5);
            if (err.cause.length > 5) specificDetails.push("... (more in console)");
          }
          else if (Array.isArray(validationErrors) && validationErrors.length > 0) {
            mainReason = mainReason.includes("Validation Errors") ? mainReason : "File validation failed.";
            specificDetails = validationErrors.map(e => `(Row ${e?.row ?? 'N/A'}) ${e?.errors || 'Details unavailable'}`).slice(0, 5);
            if (validationErrors.length > 5) specificDetails.push("... (more errors in file)");
          }

          userMessage = `Upload Failed: ${mainReason}`;
          if (specificDetails.length > 0) { userMessage += ` Details: ${specificDetails.join('; ')}`; }

          if (mainReason.toLowerCase().includes("invalid header")) { userMessage += " Check template column names."; }
          else if (mainReason.includes("No valid data rows")) { userMessage += " Check file data format/Offer ID match."; }
          else if (!navigator.onLine) { userMessage += " Check internet connection."; }

        } catch (formattingError) {
          console.error("[Upload Error] Formatting Error:", formattingError);
          userMessage = `Upload failed: ${err?.message || 'An critical unknown error occurred.'}`;
        }
        setFeedbackMessage({ type: 'error', text: userMessage });

      } finally {
        setIsUploading(false);
        try { if (repaymentFileRef?.current) repaymentFileRef.current.value = ""; }
        catch (e) { console.error("Error resetting repayment file input:", e); }
      }
    }; // end reader.onload

    reader.onerror = (err) => {
      console.error("[Upload Error] File Reading Error:", err);
      setFeedbackMessage({ type: 'error', text: `Error reading file: ${err?.message || 'Could not read the selected file.'}` });
      setIsUploading(false);
      try { if (repaymentFileRef?.current) repaymentFileRef.current.value = ""; }
      catch (e) { console.error("Error resetting repayment file input:", e); }
    };
    reader.readAsArrayBuffer(file);
  }; // --- END handleRepaymentUpload ---

  // Effect to trigger fetch when lenderId is available
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Effect to clear feedback message
  useEffect(() => {
    if (feedbackMessage.text) {
      const timer = setTimeout(() => setFeedbackMessage({ type: '', text: '' }), 5000);
      return () => clearTimeout(timer);
    }
  }, [feedbackMessage]);

  // --- Sorting and Filtering Logic ---
  // Define the desired status order arrays
  const disbursalStatusOrder = useMemo(() => [
    'LOAN_CONTRACT_ACCEPTED',
    'INITIATED_FUND_TRANSFER', // If used
    'READY_FOR_DISBURSAL',     // If used
    'LOAN_IN_PROGRESS',
    'PAID',
    'LOAN_CANCELLED',
    'WRITTEN_OFF',
    'REJECTED',
    'EXPIRED'
    // PENDING is already excluded by API
  ], []);

  const repaymentStatusOrder = useMemo(() => [
    'LOAN_IN_PROGRESS',
    'PAID', // Added 'PAID' here based on requirement 'WRITTEN_OFF_PAID' likely meaning PAID or WRITTEN_OFF
    'WRITTEN_OFF',
    'LOAN_CANCELLED',
    // Less relevant but included for completeness if needed
    'READY_FOR_DISBURSAL',
    'INITIATED_FUND_TRANSFER',
    'LOAN_CONTRACT_ACCEPTED',
    'REJECTED',
    'EXPIRED'
  ], []);


  const getSortIndex = (status, orderArray) => {
    const upperStatus = status?.toUpperCase();
    const index = orderArray.indexOf(upperStatus);
    return index === -1 ? orderArray.length : index; // Put unknown/unlisted statuses at the end
  };

  // Memoized filtered and sorted data for Disbursal section
  const filteredSortedDisbursalData = useMemo(() => {
    // Destructure ALL filters from the single state object
    const { searchText, offerStatus, invoiceStatus, disbursalDateFrom, disbursalDateTo, minAmount, maxAmount } = filters;
    const searchTextLower = searchText.toLowerCase().trim();
    const offerStatusLower = offerStatus?.toLowerCase();
    const invoiceStatusLower = invoiceStatus?.toLowerCase();
    const disbursalFromTs = disbursalDateFrom ? new Date(disbursalDateFrom).setHours(0, 0, 0, 0) : null;
    const disbursalToTs = disbursalDateTo ? new Date(disbursalDateTo).setHours(23, 59, 59, 999) : null;
    const numMinAmount = minAmount === '' ? -Infinity : parseFloat(minAmount);
    const numMaxAmount = maxAmount === '' ? Infinity : parseFloat(maxAmount);

    return allOffersData
      .filter(item => {
        if (!item?.offerInfo?.status) return false; // Ensure offerInfo and status exist
        if (!disbursalStatusOrder.includes(item.offerInfo.status)) return false;

        // Apply Text Search Filter
        if (searchTextLower) {
          const merchantName = (item.merchantInfo?.businessName || `${item.merchantInfo?.firstName || ''} ${item.merchantInfo?.lastName || ''}`).toLowerCase();
          const matchesText = (
            item.offerInfo?.offerId?.toLowerCase().includes(searchTextLower) ||
            item.merchantInfo?.merchantId?.toLowerCase().includes(searchTextLower) ||
            merchantName.includes(searchTextLower) ||
            item.invoiceInfo?.invoiceNumber?.toLowerCase().includes(searchTextLower)
          );
          if (!matchesText) return false;
        }

        // Apply Offer Status Filter
        if (offerStatus && item.offerInfo?.status?.toLowerCase() !== offerStatusLower) {
          return false;
        }

        // Apply Invoice Status Filter
        if (invoiceStatus && item.invoiceInfo?.invoiceStatus?.toLowerCase() !== invoiceStatusLower) {
          return false;
        }

        // Apply Offer Amount Filter (using creditLimit as the offer amount field)
        const offerAmount = parseFloat(item.offerInfo?.creditLimit);
        if (!isNaN(numMinAmount) && (isNaN(offerAmount) || offerAmount < numMinAmount)) return false;
        if (!isNaN(numMaxAmount) && (isNaN(offerAmount) || offerAmount > numMaxAmount)) return false;


        // Apply Disbursal Date Filter
        const disbursementDate = item.offerInfo.disbursementDetails?.timestamp
          ? new Date(item.offerInfo.disbursementDetails.timestamp)
          : null;
        if (disbursementDate) {
          const disbursementTs = disbursementDate.getTime();
          if (disbursalFromTs && disbursementTs < disbursalFromTs) return false;
          if (disbursalToTs && disbursementTs > disbursalToTs) return false;
        } else {
          if (disbursalFromTs || disbursalToTs) return false; // Exclude if filtering by date but no date exists
        }

        return true;
      })
      .sort((a, b) => { // Keep existing sort logic
        const statusAIndex = getSortIndex(a.offerInfo.status, disbursalStatusOrder);
        const statusBIndex = getSortIndex(b.offerInfo.status, disbursalStatusOrder);
        if (statusAIndex !== statusBIndex) {
          return statusAIndex - statusBIndex;
        }
        const dateA = a.offerInfo.disbursementDetails?.timestamp || a.offerInfo.createdAt;
        const dateB = b.offerInfo.disbursementDetails?.timestamp || b.offerInfo.createdAt;
        try { return new Date(dateB) - new Date(dateA); } // Newest first
        catch { return 0; } // Fallback sort
      });
  }, [allOffersData, disbursalStatusOrder, filters]); // DEPEND ON THE SINGLE 'filters' OBJECT

  // Memoized filtered and sorted data for Repayment section
  const filteredSortedRepaymentData = useMemo(() => {
    // Destructure ALL filters
    const { searchText, offerStatus, nextDueDateFrom, nextDueDateTo, minAmount, maxAmount } = filters; // InvoiceStatus/DisbursalDate not relevant here
    const searchTextLower = searchText.toLowerCase().trim();
    const offerStatusLower = offerStatus?.toLowerCase();
    const nextDueFromTs = nextDueDateFrom ? new Date(nextDueDateFrom).setHours(0, 0, 0, 0) : null;
    const nextDueToTs = nextDueDateTo ? new Date(nextDueDateTo).setHours(23, 59, 59, 999) : null;
    const numMinAmount = minAmount === '' ? -Infinity : parseFloat(minAmount);
    const numMaxAmount = maxAmount === '' ? Infinity : parseFloat(maxAmount);

    return allOffersData
      .filter(item => {
        if (!item?.offerInfo?.status) return false; // Ensure offerInfo and status exist
        if (!repaymentStatusOrder.includes(item.offerInfo.status)) return false;

        // Apply Text Search Filter (same as above)
        if (searchTextLower) {
          const merchantName = (item.merchantInfo?.businessName || `${item.merchantInfo?.firstName || ''} ${item.merchantInfo?.lastName || ''}`).toLowerCase();
          const matchesText = (
            item.offerInfo?.offerId?.toLowerCase().includes(searchTextLower) ||
            item.merchantInfo?.merchantId?.toLowerCase().includes(searchTextLower) ||
            merchantName.includes(searchTextLower) ||
            item.invoiceInfo?.invoiceNumber?.toLowerCase().includes(searchTextLower)
          );
          if (!matchesText) return false;
        }

        // Apply Offer Status Filter
        if (offerStatus && item.offerInfo?.status?.toLowerCase() !== offerStatusLower) {
          return false;
        }

        // Apply Offer Amount Filter
        const offerAmount = parseFloat(item.offerInfo?.creditLimit);
        if (!isNaN(numMinAmount) && (isNaN(offerAmount) || offerAmount < numMinAmount)) return false;
        if (!isNaN(numMaxAmount) && (isNaN(offerAmount) || offerAmount > numMaxAmount)) return false;


        // Apply Next Due Date Filter
        const sortedEmis = item.offerInfo.emiDetails?.sort((a, b) => (a.emiNumber ?? 0) - (b.emiNumber ?? 0)) || [];
        const nextPendingEmi = sortedEmis.find(emi => emi.rePaymentStatus === 'PENDING');
        let nextDueDateTs = null;
        try {
          if (nextPendingEmi?.rePaymentDate) nextDueDateTs = new Date(nextPendingEmi.rePaymentDate).getTime();
        } catch { nextDueDateTs = null; }

        if (nextDueDateTs) {
          if (nextDueFromTs && nextDueDateTs < nextDueFromTs) return false;
          if (nextDueToTs && nextDueDateTs > nextDueToTs) return false;
        } else {
          if (nextDueFromTs || nextDueToTs) return false; // Exclude if filtering by date but no date exists
        }

        return true; // Item passes all filters
      })
      .sort((a, b) => { // Keep existing sort logic
        const statusAIndex = getSortIndex(a.offerInfo.status, repaymentStatusOrder);
        const statusBIndex = getSortIndex(b.offerInfo.status, repaymentStatusOrder);
        if (statusAIndex !== statusBIndex) {
          return statusAIndex - statusBIndex;
        }
        const dateA = a.offerInfo.disbursementDetails?.timestamp || a.offerInfo.createdAt;
        const dateB = b.offerInfo.disbursementDetails?.timestamp || b.offerInfo.createdAt;
        try { return new Date(dateB) - new Date(dateA); } // Newest first
        catch { return 0; } // Fallback sort
      });
  }, [allOffersData, repaymentStatusOrder, filters]); // DEPEND ON THE SINGLE 'filters' OBJECT

  // --- Contract PDF View Handler ---
  const handleViewContractClick = (signedUrl) => {
    if (!signedUrl) {
      setFeedbackMessage({ type: 'error', text: "Contract signed URL is missing." });
      return;
    }
    try {
      window.open(signedUrl, '_blank', 'noopener,noreferrer');
    } catch (err) {
      console.error("Error opening signed URL:", err);
      setFeedbackMessage({ type: 'error', text: "Could not open contract link." });
    }
  };

  // --- Excel Download Handlers ---
  // ... (keep existing handleDownloadDisbursalTemplate and handleDownloadRepaymentTemplate)

  const handleDownloadPendingDisbursals = () => {
    setFeedbackMessage({ type: 'info', text: 'Generating pending disbursals report...' });

    // Filter data for offers pending disbursal (adjust statuses as needed)
    const pendingDisbursals = allOffersData.filter(item =>
      item.invoiceInfo?.invoiceStatus?.toUpperCase() === 'READY_FOR_DISBURSAL' &&
      item.offerInfo?.status?.toUpperCase() === 'LOAN_CONTRACT_ACCEPTED'
    );

    if (pendingDisbursals.length === 0) {
      setFeedbackMessage({ type: 'warning', text: 'No offers currently pending disbursal.' });
      return;
    }

    // Prepare data for Excel
    const dataForExcel = [
      // UPDATED: Added "Account Number", "IFSC Code" headers
      ["Offer ID", "Merchant Name", "Merchant ID", "Invoice #", "Disbursal Amount", "Offer Status", "Contract Accepted Date", "Account Number", "IBAN"]
    ];

    pendingDisbursals.forEach(item => {
      dataForExcel.push([
        item.offerInfo.offerId ?? 'N/A',
        item.merchantInfo.businessName || `${item.merchantInfo.firstName || ''} ${item.merchantInfo.lastName || ''}`,
        item.merchantInfo.merchantId ?? 'N/A',
        item.invoiceInfo.invoiceNumber ?? 'N/A',
        // Use the expected disbursal amount from contract details or fallback
        item.offerInfo.contractDetails?.disbursementAmount ?? item.offerInfo.contractDetails?.discountedAmount ?? 'N/A',
        item.offerInfo.status ?? 'N/A',
        // Use contract sign date or offer creation date as fallback for contract accepted date
        formatDate(item.offerInfo.contractDetails?.contractSignDate ?? item.offerInfo.createdAt),
        // ADDED: Populate Account Number and IFSC Code from merchantInfo
        item.merchantInfo.bankAccountNumber ?? 'N/A',
        item.merchantInfo.ifscCode ?? 'N/A'
      ]);
    });

    // Create worksheet and workbook
    const worksheet = XLSX.utils.aoa_to_sheet(dataForExcel);
    // UPDATED: Adjusted column widths array to include the new columns
    worksheet['!cols'] = [
      { wch: 25 }, // Offer ID
      { wch: 25 }, // Merchant Name
      { wch: 25 }, // Merchant ID
      { wch: 15 }, // Invoice #
      { wch: 18 }, // Disbursal Amount
      { wch: 20 }, // Offer Status
      { wch: 15 }, // Contract Accepted Date
      { wch: 20 }, // Account Number (NEW)
      { wch: 20 }  // IFSC Code (NEW)
    ];

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Pending Disbursals");

    // Generate Excel buffer and trigger download
    try {
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const dataBlob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8" });
      saveAs(dataBlob, "pending_disbursals_report.xlsx");
      setFeedbackMessage({ type: 'success', text: 'Pending disbursals report downloaded successfully.' });
    } catch (error) {
      console.error("Error generating pending disbursals Excel:", error);
      setFeedbackMessage({ type: 'error', text: 'Failed to generate pending disbursals report.' });
    }
  };
  const handleDownloadPendingEmis = () => {
    setFeedbackMessage({ type: 'info', text: 'Generating pending EMIs report...' });

    const pendingEmisData = [
      // Headers
      ["Offer ID", "Merchant Name", "Merchant ID", "Invoice #", "EMI #", "Due Date", "Amount Due", "Offer Status"]
    ];

    let foundPending = false;
    // Iterate through offers relevant for repayment
    filteredSortedRepaymentData.forEach(item => {
      if (item.offerInfo.emiDetails && Array.isArray(item.offerInfo.emiDetails)) {
        item.offerInfo.emiDetails.forEach(emi => {
          if (emi.rePaymentStatus?.toUpperCase() === 'PENDING') {
            foundPending = true;
            pendingEmisData.push([
              item.offerInfo.offerId,
              item.merchantInfo.businessName || `${item.merchantInfo.firstName} ${item.merchantInfo.lastName}`,
              item.merchantInfo.merchantId,
              item.invoiceInfo.invoiceNumber,
              emi.emiNumber,
              formatDate(emi.rePaymentDate),
              emi.rePaymentAmount ?? 'N/A', // Assuming this field holds the amount due
              item.offerInfo.status // Include offer status for context
            ]);
          }
        });
      }
    });

    if (!foundPending) {
      setFeedbackMessage({ type: 'warning', text: 'No pending EMIs found across active loans.' });
      return;
    }

    // Create worksheet and workbook
    const worksheet = XLSX.utils.aoa_to_sheet(pendingEmisData);
    // Optional: Set column widths (example)
    worksheet['!cols'] = [{ wch: 25 }, { wch: 25 }, { wch: 15 }, { wch: 15 }, { wch: 8 }, { wch: 12 }, { wch: 15 }, { wch: 20 }];

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Pending EMIs");

    // Generate Excel buffer and trigger download
    try {
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const dataBlob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8" });
      saveAs(dataBlob, "pending_emis_report.xlsx");
      setFeedbackMessage({ type: 'success', text: 'Pending EMIs report downloaded successfully.' });
    } catch (error) {
      console.error("Error generating pending EMIs Excel:", error);
      setFeedbackMessage({ type: 'error', text: 'Failed to generate pending EMIs report.' });
    }
  };

  // --- Excel Download Handlers ---
  const handleDownloadDisbursalTemplate = () => {
    // UPDATED Header to match upload expectations
    const worksheet = XLSX.utils.aoa_to_sheet([
      ["UTR", "invoiceOfferId", "disbursementTimestamp", "disbursalAmount"] // Corrected header
    ]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Disbursal Data");
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const dataBlob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8" });
    saveAs(dataBlob, "disbursal_upload_template.xlsx");
    setFeedbackMessage({ type: 'info', text: 'Disbursal template downloaded.' });
  };

  const handleDownloadRepaymentTemplate = () => {
    const worksheet = XLSX.utils.aoa_to_sheet([
      ["offerId", "emiNumber", "UTR", "paymentTimestamp", "paidAmount"]
    ]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Repayment Data");
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const dataBlob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8" });
    saveAs(dataBlob, "repayment_upload_template.xlsx"); // More specific name
    setFeedbackMessage({ type: 'info', text: 'Repayment template downloaded.' });
  };
  // --- Modal Control ---
  const openEmiModal = (offer) => {
    setSelectedOfferForModal(JSON.parse(JSON.stringify(offer))); // Use deep copy
    setShowEmiModal(true);
    setFeedbackMessage({ type: '', text: '' });
  };
  const closeEmiModal = () => {
    setShowEmiModal(false);
    setSelectedOfferForModal(null);
  };
  // --- Render Logic ---
  // Loading State
  if (loading) {
    return (<div className="flex justify-center items-center h-screen"> <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500"></div> </div>);
  }
  // Error State
  if (error) {
    return (<div className="p-6 bg-red-100 text-red-800 rounded-lg shadow-md max-w-lg mx-auto mt-10"> <h2 className="text-xl font-semibold mb-3">Error</h2> <p className="mb-4">{error}</p> <button onClick={fetchData} className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition duration-150" disabled={!lenderId} > Retry </button> </div>);
  }

  // Main component render
  return (
    <div className="p-4 md:p-6 bg-gray-100 min-h-screen w-[83vw] overflow-hidden">
      <h1 className="text-2xl md:text-3xl font-bold mb-6 text-gray-800">Disbursal and Repayment</h1>

      {/* Feedback Area */}
      {feedbackMessage.text && (
        <div className={`p-4 mb-4 rounded-md text-sm shadow ${feedbackMessage.type === 'error' ? 'bg-red-100 text-red-700' : feedbackMessage.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-blue-100 text-blue-700'}`}>
          {feedbackMessage.text}
        </div>
      )}

      <DisbursalRepaymentFilterSection
        filters={filters}
        setFilters={setFilters} // Pass the main setFilters
        resetFilters={resetFilters} // Pass the main resetFilters
        uniqueOfferStatuses={uniqueOfferStatuses} // Pass pre-calculated unique statuses
        uniqueInvoiceStatuses={uniqueInvoiceStatuses} // Pass pre-calculated unique statuses
      />

      {/* Section Tabs */}
      <div className="mb-6 flex space-x-2 border-b border-gray-300 w-full">
        <button
          className={`py-2 px-4 font-medium text-sm md:text-base focus:outline-none ${activeSection === 'disbursal' ? 'border-b-2 border-[#004141] text-[#004141]' : 'text-gray-500 hover:text-gray-700 hover:border-gray-400'}`}
          onClick={() => setActiveSection('disbursal')}
        >
          Disbursal Management
        </button>
        <button
          className={`py-2 px-4 font-medium text-sm md:text-base focus:outline-none ${activeSection === 'repayment' ? 'border-b-2 border-[#004141] text-[#004141]' : 'text-gray-500 hover:text-gray-700 hover:border-gray-400'}`}
          onClick={() => setActiveSection('repayment')}
        >
          Repayment Tracking
        </button>
      </div>

      {/* Content Area */}
      <div className="bg-white p-4 md:p-6 rounded-lg shadow-lg w-full max-w-full">
        {/* Disbursal Section */}
        {activeSection === 'disbursal' && (
          <div className="w-full">
            <h2 className="text-xl font-semibold mb-4 text-gray-700 flex items-center">
              Disbursals Overview
              <InfoIcon tooltipText="Upload Excel with columns: UTR, invoiceOfferId, disbursementTimestamp, disbursalAmount. Updates status to LOAN_IN_PROGRESS (client-side)." />
            </h2>

            {/* Buttons Section */}
            <div className="flex flex-wrap lg:flex-nowrap items-center mb-4 gap-3 w-full">
              <div className="flex items-center gap-3">
                <button
                  onClick={handleDownloadDisbursalTemplate}
                  className="px-4 py-2 bg-[#004141] text-white rounded shadow hover:bg-[#208039] transition duration-150 text-sm font-medium whitespace-nowrap"
                >
                  Download Template (.xlsx)
                </button>

                <button
                  onClick={handleDownloadPendingDisbursals}
                  className="px-4 py-2 bg-[#004141] text-white rounded shadow hover:bg-[#208039] transition duration-150 text-sm font-medium whitespace-nowrap"
                >
                  Download Pending Disbursals
                </button>
              </div>

              <div className="ml-auto">
                <label
                  htmlFor="disbursal-upload"
                  className="px-4 py-2 bg-[#004141] text-white rounded shadow hover:bg-[#208039] transition duration-150 text-sm font-medium cursor-pointer whitespace-nowrap"
                >
                  Upload Disbursal File
                </label>
                <input
                  id="disbursal-upload"
                  ref={disbursalFileRef}
                  type="file"
                  accept=".xlsx, .xls, .csv"
                  className="hidden"
                  onChange={handleDisbursalUpload}
                />
              </div>
            </div>

            {/* Table Section */}
            <div className="overflow-x-scroll mt-4 w-full custom-h-scrollbar">
              <table className="min-w-full divide-y divide-gray-200 border border-gray-200">
                {/* Corrected Header Alignments */}
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Lender</th>
                    <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Offer ID</th>
                    <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Merchant</th>
                    <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Contact</th>
                    {/* Changed Disbursal Amt header to text-right */}
                    <th className="px-2 md:px-4 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Disbursal Amt.</th>
                    <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice#</th>
                    <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice Status</th>
                    <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Offer Status</th>
                    <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Contract</th>
                    <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Disbursed Info</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSortedDisbursalData.length > 0 ? filteredSortedDisbursalData.map((item) => {
                    const lenderInfo = lenderDetailsMap[item.lenderId] || { lenderName: 'Loading...', logoUrl: null };
                    return (
                      <tr key={item.clientId} className="hover:bg-blue-50">
                        {/* Lender Column Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {lenderInfo.logoUrl ? (
                              <Image
                                unoptimized
                                src={lenderInfo.logoUrl}
                                alt={`${lenderInfo.lenderName} Logo`}
                                width={24}
                                height={24}
                                className="h-6 w-6 rounded-full mr-2 object-contain flex-shrink-0"
                              />
                            ) : (
                              <span className="h-6 w-6 rounded-full bg-gray-200 mr-2 flex items-center justify-center text-xs text-gray-500">?</span>
                            )}
                            <div className="text-sm font-medium text-gray-900 truncate" title={lenderInfo.lenderName}>
                              {lenderInfo.lenderName}
                            </div>
                          </div>
                          <div className="text-xs text-gray-500 font-mono mt-1 truncate" title={item.lenderId}>{item.lenderId}</div>
                        </td>
                        {/* Offer ID Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-xs text-gray-500 font-mono" title={item.offerInfo.offerId}>
                          {item.offerInfo.offerId}
                        </td>
                        {/* Merchant Cell - Removed the duplicate Offer ID TD before this */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{item.merchantInfo.businessName || `${item.merchantInfo.firstName} ${item.merchantInfo.lastName}`}</div>
                          <div className="text-xs text-gray-500">{item.merchantInfo.merchantId}</div>
                        </td>
                        {/* Contact Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div title={item.merchantInfo.email}>{item.merchantInfo.email}</div>
                          <div>{formatPhoneNumber(item.merchantInfo.mobileNo)}</div>
                        </td>
                        {/* Disbursal Amt Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold text-right">
                          {formatCurrency(
                            item.offerInfo.disbursementDetails?.disbursedAmount ??
                            item.offerInfo.contractDetails?.disbursementAmount ??
                            item.offerInfo.contractDetails?.discountedAmount
                          )}
                        </td>
                        {/* Invoice# Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-gray-500">{item.invoiceInfo.invoiceNumber}</td>
                        {/* Invoice Status Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-center">
                          <StatusBadge status={item.invoiceInfo.invoiceStatus} />
                        </td>
                        {/* Offer Status Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-center">
                          <StatusBadge status={item.offerInfo.status} />
                        </td>
                        {/* Contract Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-center">
                          {item.offerInfo.invoiceContract?.signedUrl ? (
                            <button
                              onClick={() => handleViewContractClick(item.offerInfo.invoiceContract.signedUrl)}
                              className="text-[#004141] hover:text-[#208039] underline text-xs font-medium"
                            >
                              View
                            </button>
                          ) : (
                            <span className="text-xs text-gray-400">N/A</span>
                          )}
                        </td>
                        {/* Disbursed Info Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-xs text-gray-500">
                          {item.offerInfo.disbursementDetails ? (
                            <>
                              UTR: {item.offerInfo.disbursementDetails.utr || 'N/A'} <br />
                              Date: {formatDate(item.offerInfo.disbursementDetails.timestamp)} <br />
                              Uploaded: {formatDate(item.offerInfo.disbursementDetails.uploadedOn)}
                            </>
                          ) : (
                            (item.offerInfo.status === 'LOAN_CONTRACT_ACCEPTED' || item.offerInfo.status === 'READY_FOR_DISBURSAL')
                              ? <span className='text-yellow-600'>Pending Upload</span>
                              : '-'
                          )}
                        </td>
                      </tr>
                    );
                  }) : (
                    <tr>
                      {/* Corrected colSpan */}
                      <td colSpan="10" className="text-center py-5 text-gray-500">No offers found matching disbursal criteria.</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Repayment Section */}
        {activeSection === 'repayment' && (
          <div className="w-full">
            <h2 className="text-xl font-semibold mb-4 text-gray-700 flex items-center">
              Repayment Tracking
              <InfoIcon tooltipText="View active loans and repayment status. Use 'Update EMIs' to view details and upload Excel with columns: offerId, emiNumber, UTR, paymentTimestamp, paidAmount." />
            </h2>

            <div className="mb-4 flex justify-start">
              <button
                onClick={handleDownloadPendingEmis}
                className="px-4 py-2 bg-[#004141] text-white rounded shadow hover:bg-[#208039] transition duration-150 text-sm font-medium"
              >
                Download Pending EMIs
              </button>
            </div>

            <div className="overflow-x-scroll mt-4 w-full custom-h-scrollbar">
              <table className="min-w-full divide-y divide-gray-200 border border-gray-200">
                {/* Corrected Header Alignments */}
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Lender</th>
                    <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Merchant</th>
                    <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Offer ID</th>
                    <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice#</th>
                    <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Offer Status</th>
                    <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Total EMIs</th>
                    <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Next Due Date</th>
                    <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSortedRepaymentData.length > 0 ? filteredSortedRepaymentData.map((item) => {
                    const lenderInfo = lenderDetailsMap[item.lenderId] || { lenderName: 'Loading...', logoUrl: null };
                    const sortedEmis = item.offerInfo.emiDetails?.sort((a, b) => a.emiNumber - b.emiNumber) || [];
                    const nextPendingEmi = sortedEmis.find(emi => emi.rePaymentStatus === 'PENDING');
                    return (
                      <tr key={item.clientId} className="hover:bg-blue-50">
                        {/* Lender Column Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {lenderInfo.logoUrl ? (
                              <Image
                                unoptimized
                                src={lenderInfo.logoUrl}
                                alt={`${lenderInfo.lenderName} Logo`}
                                width={24}
                                height={24}
                                className="h-6 w-6 rounded-full mr-2 object-contain flex-shrink-0"
                              />
                            ) : (
                              <span className="h-6 w-6 rounded-full bg-gray-200 mr-2 flex items-center justify-center text-xs text-gray-500">?</span>
                            )}
                            <div className="text-sm font-medium text-gray-900 truncate" title={lenderInfo.lenderName}>
                              {lenderInfo.lenderName}
                            </div>
                          </div>
                          <div className="text-xs text-gray-500 font-mono mt-1 truncate" title={item.lenderId}>{item.lenderId}</div>
                        </td>
                        {/* Merchant Cell (Removed the extra empty TD) */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{item.merchantInfo.businessName || `${item.merchantInfo.firstName} ${item.merchantInfo.lastName}`}</div>
                          <div className="text-xs text-gray-500" title={item.merchantInfo.merchantId}>{item.merchantInfo.merchantId}</div>
                        </td>
                        {/* Offer ID Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-xs text-gray-500 font-mono" title={item.offerInfo.offerId}>
                          {item.offerInfo.offerId}
                        </td>
                        {/* Invoice# Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-gray-500">{item.invoiceInfo.invoiceNumber}</td>
                        {/* Offer Status Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-center">
                          <StatusBadge status={item.offerInfo.status} />
                        </td>
                        {/* Total EMIs Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{sortedEmis.length || 0}</td>
                        {/* Next Due Date Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          {nextPendingEmi ? formatDate(nextPendingEmi.rePaymentDate) : 'N/A'}
                        </td>
                        {/* Actions Cell */}
                        <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-center">
                          <button
                            onClick={() => openEmiModal(item)}
                            className="px-3 py-1 bg-[#004141] text-white rounded hover:bg-[#208039] disabled:opacity-50 disabled:cursor-not-allowed transition duration-150 text-xs font-medium"
                            disabled={!item.offerInfo.emiDetails || item.offerInfo.emiDetails.length === 0}
                          >
                            Update EMIs
                          </button>
                        </td>
                      </tr>
                    );
                  }) : (
                    <tr>
                      {/* Corrected colSpan */}
                      <td colSpan="8" className="text-center py-5 text-gray-500">No offers found matching repayment criteria.</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>

      {/* EMI Update Modal */}
      {showEmiModal && selectedOfferForModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex justify-center items-center z-50 p-4 overflow-y-auto transition-opacity duration-300 ease-out" style={{ opacity: 1 }}>
          <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col transform transition-all duration-300 ease-out scale-100">
            {/* Modal Header */}
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
              <h2 className="text-lg font-semibold text-gray-800">
                Update EMI Status for Offer: <span className="font-mono">{selectedOfferForModal.offerInfo.offerId}</span>
                <span className="block text-sm text-gray-500 font-normal">
                  Merchant: {selectedOfferForModal.merchantInfo.businessName || `${selectedOfferForModal.merchantInfo.firstName} ${selectedOfferForModal.merchantInfo.lastName}`} | Invoice: {selectedOfferForModal.invoiceInfo.invoiceNumber}
                </span>
              </h2>
              <button onClick={closeEmiModal} className="text-gray-400 hover:text-gray-600">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Modal Body */}
            <div className="flex-grow overflow-y-auto p-6">
              <div className="flex flex-wrap lg:flex-nowrap items-center mb-4 gap-3">
                <button
                  onClick={handleDownloadRepaymentTemplate}
                  className="px-4 py-2 bg-[#004141] text-white rounded shadow hover:bg-[#208039] transition duration-150 text-sm font-medium"
                >
                  Download Template (.xlsx)
                </button>
                <div className="flex items-center ml-auto">
                  <label
                    htmlFor="repayment-upload"
                    className="text-center cursor-pointer px-4 py-2 bg-[#004141] text-white rounded shadow hover:bg-[#208039] transition duration-150 text-sm font-medium"
                  >
                    Upload Repayments File
                  </label>
                  <input
                    id="repayment-upload"
                    ref={repaymentFileRef}
                    type="file"
                    accept=".xlsx, .xls, .csv"
                    className="hidden"
                    onChange={handleRepaymentUpload}
                  />
                  <InfoIcon tooltipText="Upload Excel with columns: offerId, emiNumber, UTR, paymentTimestamp, paidAmount." />
                </div>
              </div>

              {/* EMI Table */}
              <div className="overflow-x-scroll mt-4 w-full border rounded-md custom-h-scrollbar">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="px-2 md:px-3 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">EMI #</th>
                      <th className="px-2 md:px-3 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Due Date</th>
                      <th className="px-2 md:px-3 py-2 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount Due</th>
                      <th className="px-2 md:px-3 py-2 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Principal</th>
                      <th className="px-2 md:px-3 py-2 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Service Fee</th>
                      <th className="px-2 md:px-3 py-2 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                      <th className="px-2 md:px-3 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Paid Date</th>
                      <th className="px-2 md:px-3 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Details (UTR)</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {Array.isArray(selectedOfferForModal.offerInfo.emiDetails) && selectedOfferForModal.offerInfo.emiDetails.length > 0 ?
                      [...selectedOfferForModal.offerInfo.emiDetails].sort((a, b) => a.emiNumber - b.emiNumber).map(emi => (
                        <tr key={emi._id || emi.emiNumber} className="hover:bg-blue-50 text-xs">
                          <td className="px-2 md:px-3 py-2 whitespace-nowrap font-medium text-gray-700">{emi.emiNumber}</td>
                          <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-600">{formatDate(emi.rePaymentDate)}</td>
                          <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-800 font-medium text-right">{formatCurrency(emi.rePaymentAmount)}</td>
                          <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-600 text-right">{formatCurrency(emi.principalRecovered, '')}</td>
                          <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-600 text-right">{formatCurrency(emi.interestAmount, '')}</td>
                          <td className="px-2 md:px-3 py-2 whitespace-nowrap text-center">
                            <StatusBadge status={emi.rePaymentStatus || 'N/A'} />
                          </td>
                          <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-600">{formatDate(emi.rePaymentActualDate)}</td>
                          <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-500 text-ellipsis overflow-hidden" title={emi.rePaymentReceivedSource || ''}>{emi.rePaymentReceivedSource}</td>
                        </tr>
                      )) : (
                        <tr>
                          <td colSpan="8" className="text-center py-4 text-gray-500 text-sm">No EMI details available for this offer.</td>
                        </tr>
                      )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end sticky bottom-0">
              <button
                onClick={closeEmiModal}
                className="px-4 py-2 bg-gray-300 text-gray-800 rounded shadow hover:bg-gray-400 transition duration-150 font-medium"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}