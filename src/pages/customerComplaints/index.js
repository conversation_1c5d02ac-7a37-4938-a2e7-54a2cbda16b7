import React, { useState, useEffect, useMemo } from 'react';
import * as XLSX from 'xlsx';
import config from "../../../config.json";
import Link from 'next/link';

// Helper function to format dates
const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return 'Invalid Date';
        return date.toLocaleString('en-GB', {
            day: '2-digit', month: 'short', year: 'numeric',
            hour: '2-digit', minute: '2-digit', hour12: true
        });
    } catch (e) {
        console.log(e);
        return 'Invalid Date';
    }
};

// --- Sub-Components ---
const ComplaintStatusBadge = ({ status }) => {
    const statusClasses = {
        'Open': 'bg-blue-100 text-blue-800',
        'Pending Customer Reply': 'bg-yellow-100 text-yellow-800',
        'Waiting for Internal Team': 'bg-purple-100 text-purple-800',
        'Resolved': 'bg-green-100 text-green-800',
        'Closed': 'bg-gray-200 text-gray-800',
        'Reopened': 'bg-pink-100 text-pink-800',
    };
    return <span className={`px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status] || 'bg-gray-100 text-gray-800'}`}>{status || 'N/A'}</span>;
};

const PriorityBadge = ({ priority }) => {
    const priorityClasses = {
        'Low': 'bg-gray-100 text-gray-700',
        'Medium': 'bg-yellow-100 text-yellow-700',
        'High': 'bg-orange-100 text-orange-700',
        'Urgent': 'bg-red-100 text-red-700',
    };
    return <span className={`px-2 py-0.5 inline-flex items-center text-xs font-medium rounded-md ${priorityClasses[priority] || 'bg-gray-100 text-gray-700'}`}>{priority || 'N/A'}</span>;
};


// --- Main Component ---
export default function CustomerComplaintLog() {
    const [tickets, setTickets] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [isExporting, setIsExporting] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedTicket, setSelectedTicket] = useState(null);

    const [filters, setFilters] = useState({ searchTerm: '', status: 'All', category: 'All' });
    const [sortConfig, setSortConfig] = useState({ key: 'createdAt', direction: 'descending' });

    const TICKET_STATUS_OPTIONS = ['All', 'Open', 'Pending Customer Reply', 'Waiting for Internal Team', 'Resolved', 'Closed', 'Reopened'];
    const TICKET_CATEGORY_OPTIONS = [
        'All',
        'Account & Profile',
        'Loan Application',
        'Invoice Financing',
        'Payments & Transactions',
        'KYC & Compliance',
        'Technical Issue',
        'Feedback & Suggestions',
        'Dispute - Invoice Discrepancies',
        'Dispute - Financing & Fees',
        'Dispute - Resolution Process',
        'Other'
    ];

    useEffect(() => {
        const fetchTickets = async () => {
            setIsLoading(true);
            setError(null);
            try {
                const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/support-tickets/all-with-details`);
                if (!response.ok) throw new Error((await response.json()).message || 'Failed to fetch');
                setTickets(await response.json());
            } catch (err) {
                setError(err.message);
            } finally {
                setIsLoading(false);
            }
        };
        fetchTickets();
    }, []);

    const handleFilterChange = (e) => setFilters(prev => ({ ...prev, [e.target.name]: e.target.value }));
    const handleViewComplaint = (ticket) => { setSelectedTicket(ticket); setIsModalOpen(true); };
    const handleCloseModal = () => setIsModalOpen(false);

    const filteredAndSortedTickets = useMemo(() => {
        let data = [...tickets];
        if (filters.status !== 'All') data = data.filter(t => t.status === filters.status);
        if (filters.category !== 'All') data = data.filter(t => t.category === filters.category);
        if (filters.searchTerm) {
            const term = filters.searchTerm.toLowerCase();
            data = data.filter(t =>
                t.ticketId?.toLowerCase().includes(term) ||
                t.userDisplayInfo?.businessName?.toLowerCase().includes(term) ||
                t.userDisplayInfo?.uniqueId?.toLowerCase().includes(term) // Added this line for uniqueId search
            );
        }
        if (sortConfig.key) {
            data.sort((a, b) => {
                let aValue = a[sortConfig.key];
                let bValue = b[sortConfig.key];

                // Handle nested sorting keys like 'userDisplayInfo.businessName' or 'userDisplayInfo.uniqueId'
                if (sortConfig.key.includes('.')) {
                    const keys = sortConfig.key.split('.');
                    aValue = keys.reduce((obj, key) => obj && obj[key], a);
                    bValue = keys.reduce((obj, key) => obj && obj[key], b);
                }

                if (aValue < bValue) return sortConfig.direction === 'ascending' ? -1 : 1;
                if (aValue > bValue) return sortConfig.direction === 'ascending' ? 1 : -1;
                return 0;
            });
        }
        return data;
    }, [tickets, filters, sortConfig]);

    const requestSort = (key) => {
        setSortConfig(prev => ({
            key,
            direction: prev.key === key && prev.direction === 'ascending' ? 'descending' : 'ascending'
        }));
    };

    const handleExport = () => {
        if (!filteredAndSortedTickets.length) return;
        setIsExporting(true);
        const dataToExport = filteredAndSortedTickets.map(t => ({
            "Complaint Reference ID": t.ticketId,
            "Customer ID": t.userDisplayInfo?.uniqueId || 'N/A', // Included uniqueId
            "Complainant Name (MSME)": t.userDisplayInfo?.businessName || 'N/A',
            "Complaint Category": t.category,
            "Submission Date": formatDate(t.createdAt),
            "Status": t.status,
            "Subject": t.subject,
        }));
        const ws = XLSX.utils.json_to_sheet(dataToExport);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "Complaint Log");
        XLSX.writeFile(wb, `Customer_Complaint_Log_${new Date().toISOString().slice(0, 10)}.xlsx`); // Added date to filename
        setIsExporting(false);
    };

    if (isLoading) return <div className="flex justify-center items-center h-screen"><div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div></div>;
    if (error) return <div className="p-6 bg-red-50 text-red-800 rounded-lg max-w-2xl mx-auto mt-10">Error: {error}</div>;

    return (
        <>
            <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 min-h-screen">
                <div className="max-w-7xl mx-auto">
                    <Link href="/reports">
                        <button className="mb-4 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50">
                            ← Back to Reports
                        </button>
                    </Link>
                    <div className="sm:flex sm:items-center sm:justify-between mb-6">
                        <h1 className="text-2xl font-bold text-gray-900">Customer Complaint Log</h1>
                        <button onClick={handleExport} disabled={isExporting} className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 disabled:opacity-50">
                            {isExporting ? 'Exporting...' : 'Export to Excel'}
                        </button>
                    </div>

                    <div className="bg-white p-4 rounded-lg shadow-sm mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <input type="text" name="searchTerm" value={filters.searchTerm} onChange={handleFilterChange} placeholder="Search Ref ID, MSME Name, Customer ID..." className="block w-full rounded-md border-gray-300 shadow-sm" />
                        <select name="category" value={filters.category} onChange={handleFilterChange} className="block w-full rounded-md border-gray-300 shadow-sm">{TICKET_CATEGORY_OPTIONS.map(opt => <option key={opt} value={opt}>{opt}</option>)}</select>
                        <select name="status" value={filters.status} onChange={handleFilterChange} className="block w-full rounded-md border-gray-300 shadow-sm">{TICKET_STATUS_OPTIONS.map(opt => <option key={opt} value={opt}>{opt}</option>)}</select>
                    </div>

                    <div className="bg-white rounded-lg shadow-md overflow-hidden">
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-100">
                                    <tr>
                                        {[
                                            { k: 'ticketId', l: 'Reference ID' },
                                            { k: 'userDisplayInfo.uniqueId', l: 'Customer ID' }, // New table header for Unique ID
                                            { k: 'userDisplayInfo.businessName', l: 'Complainant Name (MSME)' },
                                            { k: 'category', l: 'Category' },
                                            { k: 'createdAt', l: 'Submission Date' },
                                            { k: 'status', l: 'Status' },
                                            { k: 'actions', l: 'Actions' }
                                        ].map(h =>
                                            <th key={h.k} scope="col" onClick={() => h.k !== 'actions' && requestSort(h.k)} className={`px-4 py-3 text-left text-xs font-bold text-gray-600 uppercase ${h.k !== 'actions' && 'cursor-pointer'}`}>
                                                {h.l}
                                                {sortConfig.key === h.k && (
                                                    <span>{sortConfig.direction === 'ascending' ? ' ▲' : ' ▼'}</span>
                                                )}
                                            </th>
                                        )}
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {filteredAndSortedTickets.length > 0 ? filteredAndSortedTickets.map((ticket) => (
                                        <tr key={ticket.ticketId} className="hover:bg-gray-50">
                                            <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-800">{ticket.ticketId}</td>
                                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">{ticket.userDisplayInfo?.uniqueId || 'N/A'}</td> {/* Display Unique ID */}
                                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">{ticket.userDisplayInfo?.businessName || 'N/A'}</td>
                                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">{ticket.category}</td>
                                            <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">{formatDate(ticket.createdAt)}</td>
                                            <td className="px-4 py-4 whitespace-nowrap text-sm"><ComplaintStatusBadge status={ticket.status} /></td>
                                            <td className="px-4 py-4 whitespace-nowrap text-sm"><button onClick={() => handleViewComplaint(ticket)} className="text-indigo-600 hover:underline font-medium">View Details</button></td>
                                        </tr>
                                    )) : <tr><td colSpan="7" className="text-center py-10 text-gray-500">No complaints found.</td></tr>}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            {isModalOpen && selectedTicket && (
                <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
                    <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
                        <div className="px-6 py-4 border-b flex justify-between items-center">
                            <h2 className="text-lg font-bold text-gray-900">Complaint Details</h2>
                            <button onClick={handleCloseModal} className="text-gray-600 hover:text-gray-900 text-2xl font-bold">&times;</button>
                        </div>
                        <div className="p-6 overflow-y-auto space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div><strong>Ref ID:</strong> {selectedTicket.ticketId}</div>
                                <div><strong>Customer ID:</strong> {selectedTicket.userDisplayInfo?.uniqueId || 'N/A'}</div> {/* Display Unique ID in modal */}
                                <div><strong>MSME Name:</strong> {selectedTicket.userDisplayInfo?.businessName || 'N/A'}</div>
                                <div><strong>Submitted By:</strong> {selectedTicket.userDisplayInfo?.fullName || 'N/A'}</div>
                                <div><strong>Contact Email:</strong> {selectedTicket.userDisplayInfo?.email || 'N/A'}</div>
                                <div className="flex items-center gap-2"><strong>Priority:</strong> <PriorityBadge priority={selectedTicket.priority} /></div>
                                <div className="flex items-center gap-2"><strong>Status:</strong> <ComplaintStatusBadge status={selectedTicket.status} /></div>
                            </div>
                            <div className="border-t pt-4">
                                <h3 className="font-semibold text-gray-800">Subject: {selectedTicket.subject}</h3>
                                <p className="mt-2 text-gray-600 whitespace-pre-wrap">{selectedTicket.description}</p>
                            </div>
                            {selectedTicket.attachments?.length > 0 && (
                                <div className="border-t pt-4">
                                    <h3 className="font-semibold text-gray-800 mb-2">Attachments</h3>
                                    <ul className="space-y-2">{selectedTicket.attachments.map((doc, i) => <li key={i}><a href={doc.signedUrl} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:underline">{doc.fileName || `Attachment ${i + 1}`}</a></li>)}</ul>
                                </div>
                            )}
                            {selectedTicket.communicationLog?.length > 0 && (
                                <div className="border-t pt-4">
                                    <h3 className="font-semibold text-gray-800 mb-2">Communication Log</h3>
                                    <div className="space-y-4 max-h-60 overflow-y-auto pr-2">{selectedTicket.communicationLog.map((log, i) => <div key={i} className={`p-3 rounded-lg ${log.senderType === 'User' ? 'bg-gray-100' : 'bg-blue-50'}`}><p className="font-bold text-xs">{log.senderName || log.senderType} <span className="font-normal text-gray-500">- {formatDate(log.timestamp)}</span></p><p className="mt-1 text-gray-700">{log.message}</p></div>)}</div>
                                </div>
                            )}
                        </div>
                        <div className="px-6 py-3 bg-gray-50 border-t flex justify-end">
                            <button onClick={handleCloseModal} className="bg-white py-2 px-4 border border-gray-300 rounded-md text-sm font-medium hover:bg-gray-50">Close</button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}