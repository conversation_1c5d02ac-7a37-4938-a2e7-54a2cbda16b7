import React from 'react';

/**
 * Helper function to format phone numbers with proper display and clickable links
 * @param {string|number} phoneNumber - The phone number to format
 * @returns {JSX.Element} - Formatted phone number with clickable tel: link
 */
export const formatPhoneNumber = (phoneNumber) => {
  if (!phoneNumber || phoneNumber === 'N/A' || phoneNumber === '') {
    return <span className="text-gray-500 italic">Not Provided</span>;
  }

  // Clean the phone number (remove spaces, dashes, parentheses)
  const cleanNumber = phoneNumber.toString().replace(/[\s\-\(\)]/g, '');
  
  // Check if it already starts with +
  let formattedNumber = cleanNumber;
  if (!cleanNumber.startsWith('+')) {
    // If it doesn't start with +, assume it's a Qatar number and add +974
    if (cleanNumber.startsWith('974')) {
      formattedNumber = '+' + cleanNumber;
    } else if (cleanNumber.length === 8) {
      // Qatar mobile numbers are typically 8 digits
      formattedNumber = '+974 ' + cleanNumber;
    } else {
      // For other cases, just add + at the beginning
      formattedNumber = '+' + cleanNumber;
    }
  } else {
    // If it already starts with +, format it nicely
    if (formattedNumber.startsWith('+974')) {
      const numberPart = formattedNumber.substring(4);
      formattedNumber = '+974 ' + numberPart;
    }
  }

  // Create the tel: link for calling
  const telLink = 'tel:' + cleanNumber.replace(/^\+/, '');

  return (
    <a
      href={telLink}
      className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
      title={`Call ${formattedNumber}`}
    >
      {formattedNumber}
    </a>
  );
};

export default formatPhoneNumber;
