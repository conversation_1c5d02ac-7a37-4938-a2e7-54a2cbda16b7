import { NextResponse } from 'next/server';

export default function middleware(request) {
  const { pathname } = request.nextUrl;
  const isLoginPage = pathname === '/';
  const isApiRoute = pathname.startsWith('/api');
  const isStaticFile = pathname.startsWith('/_next') || pathname.startsWith('/favicon.ico');
  
  // Skip middleware for API routes and static files
  if (isApiRoute || isStaticFile) {
    return NextResponse.next();
  }

  const userId = request.cookies.get('userId')?.value;

  // If not logged in and trying to access protected routes
  if (!userId && !isLoginPage) {
    const url = new URL('/', request.url);
    return NextResponse.redirect(url);
  }

  // If logged in and trying to access login page
  if (userId && isLoginPage) {
    const url = new URL('/dashboard', request.url);
    return NextResponse.redirect(url);
  }

  return NextResponse.next();
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}; 