import React from 'react';
import {
    CheckCircleIcon,      // Green for success/approved/verified/clear
    XCircleIcon,          // Red for rejected/error/hit/failed
    ClockIcon,            // Yellow/Blue/Gray for pending/initiated/in progress
    QuestionMarkCircleIcon, // Default/Unknown
    ArrowPathIcon,        // Yellow for under review, Blue for reinitiated
    ExclamationTriangleIcon, // Orange for info needed/expired, Red for HIT
    InformationCircleIcon, // Blue for submitted, Gray for skipped/not attempted
    VideoCameraSlashIcon,  // For FAILED_ATTEMPT (Video KYC)
    FolderOpenIcon,        // For NOT_ATTEMPTED / NOT_CHECKED
    DocumentMagnifyingGlassIcon // For PENDING_REVIEW
} from '@heroicons/react/24/outline';

/**
 * Returns appropriate CSS classes for status styling
 * @param {string} status - The status to get classes for
 * @returns {string} - CSS class string
 */
export const getStatusClass = (status) => {
    const upperStatus = status?.toUpperCase() || 'UNKNOWN';
    if (upperStatus === 'QATAR') {
        return 'text-green-800 bg-green-100'; // Green for within Qatar
    }
    if (upperStatus.startsWith('LOCATED IN')) {
        return 'text-red-800 bg-red-100';     // Red for outside Qatar
    }
    switch (upperStatus) {
        // Offer/Loan Statuses
        case 'QATAR': return 'text-green-800 bg-green-100';
        case 'ACTIVE': return 'text-green-800 bg-green-100';
        case 'APPOVAL_PENDING': return 'text-yellow-800 bg-yellow-100'; // ADDED/MOVED here for yellow
        case 'LOAN_CONTRACT_ACCEPTED': return 'text-blue-800 bg-blue-100';
        case 'READY_FOR_DISBURSAL': return 'text-yellow-800 bg-yellow-100';
        case 'INITIATED_FUND_TRANSFER': return 'text-cyan-800 bg-cyan-100';
        case 'LOAN_IN_PROGRESS': return 'text-purple-800 bg-purple-100';
        case 'PAID': return 'text-green-800 bg-green-100';
        case 'REJECTED':
        case 'LOAN_CANCELLED': return 'text-red-800 bg-red-100';
        case 'EXPIRED':
        case 'WRITTEN_OFF': return 'text-gray-800 bg-gray-400';

        // EMI Statuses
        case 'PENDING': return 'text-yellow-800 bg-yellow-100';
        case 'PARTIAL': return 'text-purple-800 bg-purple-100';

        // Invoice Statuses
        case 'VERIFICATION_PENDING_ANCHOR': return 'text-yellow-800 bg-yellow-100'; // Changed from gray to yellow
        case 'VERIFIED_ANCHOR': return 'text-green-800 bg-green-100';
        case 'VERIFICATION_PENDING_LENDER': return 'text-teal-800 bg-teal-100';
        case 'ACCEPTED_LENDER': return 'text-green-800 bg-green-100';
        case 'REJECTED_ANCHOR':
        case 'REJECTED_LENDER': return 'text-red-800 bg-red-100';

        // KYC Statuses
        case 'APPROVED': return 'text-green-800 bg-green-100';
        case 'PASSED': return 'text-green-800 bg-green-100';
        case 'VERIFIED': return 'text-green-800 bg-green-100';
        case 'UNDER_REVIEW': return 'text-yellow-800 bg-yellow-100';
        case 'REVIEW': return 'text-yellow-800 bg-yellow-100';
        case 'INFO_NEEDED': return 'text-orange-800 bg-orange-100';
        case 'MORE_INFO_NEEDED_ANCHOR':
        case 'MORE_INFO_NEEDED_LENDER': return 'text-orange-800 bg-orange-100';
        case 'SUBMITTED': return 'text-blue-700 bg-blue-100';
        case 'INITIATED': return 'text-gray-800 bg-gray-100';
        case 'REINITIATED': return 'text-blue-800 bg-blue-100';
        case 'DISBURSED': return 'text-teal-800 bg-teal-100';

        // New KYC Specific Statuses
        case 'NOT_CHECKED': return 'text-gray-700 bg-gray-200';
        case 'HIT': return 'text-red-700 bg-red-200';
        case 'SKIPPED': return 'text-gray-600 bg-gray-100';
        case 'NOT_ATTEMPTED': return 'text-gray-700 bg-gray-200';
        case 'FAILED_ATTEMPT': return 'text-red-700 bg-red-100';
        case 'CLEAR': return 'text-green-800 bg-green-100';
        case 'ERROR': return 'text-red-800 bg-red-100';
        case 'PENDING_REVIEW': return 'text-yellow-800 bg-yellow-100';

        // Default
        default: return 'text-gray-800 bg-gray-100';
    }
};

/**
 * Renders a status badge with appropriate icon and styling based on status
 * @param {string} status - The status to display
 * @returns {JSX.Element} - Rendered status badge with icon
 */
const StatusBadge = ({ status }) => {
    const upperStatus = status?.toUpperCase() || 'UNKNOWN';
    let colorClass = getStatusClass(upperStatus);
    let icon = <QuestionMarkCircleIcon className="w-4 h-4 mr-1.5 text-gray-500" aria-hidden="true" />; // Default icon
    let statusText = status || 'UNKNOWN'; // Handle null/undefined status

    // Determine appropriate icon and display text based on status
    switch (upperStatus) {
        // Completed/Success statuses
        case 'PAID':
        case 'PASSED':
        case 'ACTIVE':
        // REMOVED 'APPROVAL_PENDING' from here as it should be pending/yellow
        case 'VERIFIED_ANCHOR':
        case 'ACCEPTED_LENDER':
        case 'APPROVED':
        case 'VERIFIED':
        case 'DISBURSED':
        case 'QATAR':
        case 'CLEAR': // AML Status
            icon = <CheckCircleIcon className="w-4 h-4 mr-1.5 text-green-500" aria-hidden="true" />;
            if (upperStatus === 'VERIFIED_ANCHOR') statusText = 'Verified by You';
            else if (upperStatus === 'ACCEPTED_LENDER') statusText = 'Accepted by Lender';
            else if (upperStatus === 'CLEAR') statusText = 'AML Clear';
            else if (upperStatus === 'ACTIVE') statusText = 'Active';
            else if (upperStatus === 'PAID') statusText = 'Paid';
            else if (upperStatus === 'DISBURSED') statusText = 'Disbursed';
            else statusText = 'Approved';
            break;

        // In-progress statuses
        case 'LOAN_IN_PROGRESS':
            icon = <ArrowPathIcon className="w-4 h-4 mr-1.5 text-purple-500" aria-hidden="true" />;
            statusText = 'Loan In Progress';
            break;

        case 'LOAN_CONTRACT_ACCEPTED':
            icon = <ArrowPathIcon className="w-4 h-4 mr-1.5 text-blue-500" aria-hidden="true" />;
            statusText = 'Contract Accepted';
            break;

        case 'REINITIATED':
            icon = <ArrowPathIcon className="w-4 h-4 mr-1.5 text-blue-500" aria-hidden="true" />;
            statusText = 'Re-initiated';
            break;

        case 'UNDER_REVIEW':
        case 'REVIEW':
            icon = <ArrowPathIcon className="w-4 h-4 mr-1.5 text-yellow-500" aria-hidden="true" />;
            statusText = 'Under Review';
            break;

        case 'SUBMITTED':
            icon = <InformationCircleIcon className="w-4 h-4 mr-1.5 text-blue-500" aria-hidden="true" />;
            statusText = 'Submitted';
            break;

        // Waiting/Pending statuses (corrected these)
        case 'APPOVAL_PENDING': // MOVED here for yellow icon
            icon = <ClockIcon className="w-4 h-4 mr-1.5 text-yellow-500" aria-hidden="true" />;
            statusText = 'Approval Pending';
            break;
        case 'PENDING':
            icon = <ClockIcon className="w-4 h-4 mr-1.5 text-yellow-500" aria-hidden="true" />;
            statusText = 'Pending';
            break;

        case 'PENDING_REVIEW':
            icon = <DocumentMagnifyingGlassIcon className="w-4 h-4 mr-1.5 text-yellow-500" aria-hidden="true" />;
            statusText = 'Pending Review';
            break;

        case 'READY_FOR_DISBURSAL':
            icon = <ClockIcon className="w-4 h-4 mr-1.5 text-yellow-500" aria-hidden="true" />;
            statusText = 'Ready For Disbursal';
            break;

        case 'VERIFICATION_PENDING_LENDER':
            icon = <ClockIcon className="w-4 h-4 mr-1.5 text-teal-500" aria-hidden="true" />;
            statusText = 'Pending Lender Verification';
            break;

        case 'VERIFICATION_PENDING_ANCHOR': // Icon color explicitly set to yellow
            icon = <ClockIcon className="w-4 h-4 mr-1.5 text-yellow-500" aria-hidden="true" />;
            statusText = 'Pending Buyer Verification';
            break;

        case 'INITIATED_FUND_TRANSFER':
            icon = <ClockIcon className="w-4 h-4 mr-1.5 text-cyan-500" aria-hidden="true" />;
            statusText = 'Initiated Fund Transfer';
            break;

        case 'INITIATED':
            icon = <ClockIcon className="w-4 h-4 mr-1.5 text-gray-500" aria-hidden="true" />;
            statusText = 'Initiated';
            break;

        // Warning/Attention statuses
        case 'PARTIAL':
            icon = <ExclamationTriangleIcon className="w-4 h-4 mr-1.5 text-purple-500" aria-hidden="true" />;
            statusText = 'Partial';
            break;

        case 'INFO_NEEDED':
        case 'MORE_INFO_NEEDED_ANCHOR':
        case 'MORE_INFO_NEEDED_LENDER':
            icon = <ExclamationTriangleIcon className="w-4 h-4 mr-1.5 text-orange-500" aria-hidden="true" />;
            statusText = 'Info Needed';
            break;

        case 'EXPIRED':
            icon = <ClockIcon className="w-4 h-4 mr-1.5 text-orange-500" aria-hidden="true" />;
            statusText = 'Expired';
            break;

        // Error/Rejected statuses
        case 'REJECTED':
        case 'REJECTED_ANCHOR':
        case 'REJECTED_LENDER':
            icon = <XCircleIcon className="w-4 h-4 mr-1.5 text-red-500" aria-hidden="true" />;
            if (upperStatus.includes('_ANCHOR')) statusText = 'Rejected by You';
            else if (upperStatus.includes('_LENDER')) statusText = 'Rejected by Lender';
            else statusText = 'Rejected';
            break;

        case 'ERROR':
            icon = <XCircleIcon className="w-4 h-4 mr-1.5 text-red-500" aria-hidden="true" />;
            statusText = 'Error';
            break;

        case 'LOAN_CANCELLED':
            icon = <XCircleIcon className="w-4 h-4 mr-1.5 text-red-500" aria-hidden="true" />;
            statusText = 'Loan Cancelled';
            break;

        case 'WRITTEN_OFF':
            icon = <XCircleIcon className="w-4 h-4 mr-1.5 text-gray-500" aria-hidden="true" />;
            statusText = 'Written Off';
            break;

        case 'HIT':
            icon = <ExclamationTriangleIcon className="w-4 h-4 mr-1.5 text-red-600" aria-hidden="true" />;
            statusText = 'AML Hit';
            break;
        // Removed the duplicate 'APPOVAL_PENDING' case from here

        // Special KYC specific statuses
        case 'NOT_CHECKED':
        case 'NOT_ATTEMPTED':
            icon = <FolderOpenIcon className="w-4 h-4 mr-1.5 text-gray-500" aria-hidden="true" />;
            statusText = upperStatus === 'NOT_CHECKED' ? 'Not Detected' : 'Not Attempted';
            break;

        case 'SKIPPED':
            icon = <InformationCircleIcon className="w-4 h-4 mr-1.5 text-gray-500" aria-hidden="true" />;
            statusText = 'Skipped';
            break;

        case 'FAILED_ATTEMPT':
            icon = <VideoCameraSlashIcon className="w-4 h-4 mr-1.5 text-red-500" aria-hidden="true" />;
            statusText = 'Failed Attempt';
            break;

        // Default for unknown statuses
        default:
            statusText = status ? status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Unknown';
            break;
    }

    return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass} whitespace-nowrap`}>
            {icon}
            {statusText}
        </span>
    );
};

export default StatusBadge;