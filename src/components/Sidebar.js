import React from 'react';
import Link from 'next/link';
import { useAuth } from '../contexts/authContext';
import {
  CheckCircleIcon,
  HomeIcon,
  DocumentChartBarIcon,
  DocumentTextIcon,
  DocumentMagnifyingGlassIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';

const Sidebar = ({ activePage }) => {
  const { user, logout } = useAuth();

  const allMenuItems = [
    {
      name: 'Dashboard Overview',
      icon: HomeIcon,
      path: '/dashboard',
      roles: ['superadmin', 'buyerAdmin', 'lenderAdmin', 'complianceOfficer'],
    },
    {
      name: 'KYC Approval',
      icon: CheckCircleIcon,
      path: '/kycApprovals',
      roles: ['superadmin', 'buyerAdmin', 'lenderAdmin'],
    },
    {
      name: 'Credit Assessment',
      icon: DocumentMagnifyingGlassIcon,
      path: '/creditAssessment',
      roles: ['superadmin', 'buyerAdmin', 'lenderAdmin'],
    },
    {
      name: 'Credit Line Activations',
      icon: CheckCircleIcon,
      path: '/creditLineActivation',
      roles: ['superadmin', 'buyerAdmin', 'lenderAdmin'],
    },
    {
      name: 'Invoices',
      icon: DocumentMagnifyingGlassIcon,
      path: '/invoices',
      roles: ['superadmin', 'buyerAdmin', 'lenderAdmin'],
    },
    {
      name: 'Payments',
      icon: DocumentTextIcon,
      path: '/payments',
      roles: ['superadmin', 'buyerAdmin', 'lenderAdmin'],
    },
    {
      name: 'Reports',
      icon: DocumentChartBarIcon,
      path: '/reports',
      roles: ['superadmin', 'buyerAdmin', 'lenderAdmin', 'complianceOfficer'],
    },
  ];

  const menuItems = allMenuItems.filter(item =>
    item.roles.includes(user?.role)
  );

  // Check if current page is a report page (only the ones that are now in the Reports section)
  const isReportPage = [
    '/reports',
    '/featuresTracker',
    '/customerReport',
    '/buyerReport'
  ].includes(activePage);

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="h-full w-64 bg-[#004141] text-white flex flex-col flex-shrink-0">
      {/* User Info Section */}
      <div className="p-4 border-b border-[#002D2D]">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-[#6fb468] rounded-full flex items-center justify-center">
            <span className="text-white font-semibold text-lg">
              {user?.username?.charAt(0).toUpperCase() || 'U'}
            </span>
          </div>
          <div>
            <p className="text-sm font-medium">{user?.username}</p>
            <p className="text-xs text-gray-300 capitalize">{user?.role}</p>
          </div>
        </div>
      </div>

      {/* Main Content with scrollable nav and sticky logout */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Scrollable Menu */}
        <nav className="flex-1 overflow-y-auto mt-5 px-2 space-y-1">
          {menuItems.map((item) => {
            // Special handling for Reports menu item
            const isActive = item.path === '/reports'
              ? isReportPage
              : activePage === item.path;

            const IconComponent = item.icon;

            return (
              <Link
                key={item.name}
                href={item.path}
                className={`flex items-center px-4 py-3 text-base font-medium rounded-md group transition-colors ${isActive
                  ? 'bg-[#6fb468] text-white font-bold'
                  : 'text-white hover:bg-[#002D2D]'
                  }`}
              >
                <IconComponent className="mr-3 h-5 w-5 text-white" />
                {item.name}
                {/* Add indicator for Reports if it has sub-items */}
                {item.name === 'Reports' && (
                  <div className="ml-auto">
                    <svg
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                )}
              </Link>
            );
          })}
        </nav>

        {/* Always-visible Logout */}
        <div className="p-4 border-t border-[#002D2D]">
          <button
            onClick={handleLogout}
            className="flex items-center w-full px-4 py-3 text-base font-medium rounded-md text-white hover:bg-[#002D2D] transition-colors"
          >
            <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-white" />
            Logout
          </button>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;