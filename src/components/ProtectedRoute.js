// components/ProtectedRoute.js
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../contexts/authContext';

const ProtectedRoute = ({ children, allowedRoles = [] }) => {
  const { user, loading, isAuthenticated, hasRole } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      // If user is not authenticated, redirect to login
      if (!isAuthenticated()) {
        router.push('/login');
        return;
      }

      // If specific roles are required and user doesn't have them, redirect to dashboard
      if (allowedRoles.length > 0 && !hasRole(allowedRoles)) {
        router.push('/dashboard');
        return;
      }
    }
  }, [user, loading, router, isAuthenticated, hasRole, allowedRoles]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#6fb468]"></div>
      </div>
    );
  }

  // Show nothing while redirecting
  if (!isAuthenticated() || (allowedRoles.length > 0 && !hasRole(allowedRoles))) {
    return null;
  }

  return children;
};

export default ProtectedRoute;