Features and Defects Tracker,,,,,,Features and Defects Tracker,,,,,,,,
Issue ID,Issue Type,Module/ Feature,Description,Severity (Critical /High/ Medium/Low),Status (New/ In Progress/ Fixed),Steps to Reproduce/ Change,Assigned To,Reported By,Reported Date,Env (Dev/ UAT/ Prod),Resolved Date,Resolution Comments,Age,PR
It1-001,Defect,User Onboarding & KYC,Dropdown for countries only reflecting Qatar/ India. Need it for all. ,High,Fixed,"1. Go to login page 
 2. Drop down countries for mobile",Aryan,Nishant,31-Mar-25,Dev,1-Apr-25,Added Country codes of all country,1,
It1-002,Defect,User Onboarding & KYC,OTP For India need to be removed and hard coded ones till it moves to test,Medium,Fixed,"Go to mobile login, Put in mobile number to ",<PERSON><PERSON>,<PERSON><PERSON><PERSON>,31-Mar-25,Dev,1-Apr-25,Commented OTP sending API,1,
It1-003,Feature,User Onboarding & KYC,"Sign Up button mapping: Logic now: 
For mobile or email signup - entire form (same) comes with verification of OTP for both email and mobile
",High,Fixed,"Click in signup, sing up logic not being clear with social media login. between sign-up Vs logging in.",<PERSON><PERSON>,<PERSON><PERSON><PERSON>,31-Mar-25,<PERSON>,2-Apr-25,Need to review once,2,
It1-004,Feature,User Onboarding & KYC,No Validation on mandatory fields found,Medium,Fixed,/kyc/person-info -> Current Address,Aryan,Nishant,31-Mar-25,Dev,2-Apr-25,Mandatory fields validation done i.e. wouldn't allow to go forward. But content wise validation is pending. Another ticket to be added.,2,
It1-005,Feature,User Onboarding & KYC,Uploaded documents are showing document preview with extra upload button. Once the PDF or doc is uploaded then a button to upload at the end of PDF should be visible,Medium,Fixed,,Aryan,Nishant,31-Mar-25,Dev,2-Apr-25,,2,
It1-006,Feature,User Onboarding & KYC,"In helper text we sould add zone , and in city we should add municipality",Low,Fixed,,Aryan,Nishant,31-Mar-25,Dev,2-Apr-25,,2,
It1-007,Defect,User Onboarding & KYC,"Terms and condition / Privacy polity/ financial beauro UI needs to be fixed -> CSS is not good, proper header and footer should be there",High,Fixed,,Aryan,Nishant,31-Mar-25,Dev,2-Apr-25,,2,
It1-008,Feature,User Onboarding & KYC,"Term & Conditions, Privacy, Financial Data Consent  are India specific - need to be updated for Qatar and Madad Fintech",High,Fixed,Checkbox at the end of onboarding ,Aryan,Nishant,31-Mar-25,Dev,2-Apr-25,"Qatar pp and tnc, sir will provide soon, rn added a country based condition to show different component based on country. Fundfina T&C, Privacy policy taken as a base with universal find & replace of Fundfina with Madad. Upfront Fundfina TEchnologies Pvt Ltd changed to Madad.  Financial Technologies. All India changed to Qatar. Address of Madad changed. ",2,
It1-009,Defect,Lender Portal,Pending kyc is not showing in lender dashboard when created in credit module,Medium,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,5-Apr-25,,5,
It1-010,Feature,Lender Portal,Total Credit Limit should be changed to Total Credit Exposure on Madad. is it for specific lender or overall,Medium,Fixed,"After logging as lender, the main dashboard",Ishaan,Nishant,31-Mar-25,Dev,10-May-25,,40,
It1-011,Defect,Lender Portal,Address information is all over - keep it consistent with user inputs ,Medium,Fixed,"As a lender , go to to be reviewed KYC users and review the KYC   -> kyc review screen -> Lender module ",Ishaan,Nishant,31-Mar-25,Dev,4-Apr-25,,4,
It1-012,Defect,Lender Portal,"Consistency of userview / admin view should be same, as we're taking information | should display like that",Medium,Fixed,Lender/ Credit Admin View --> When reivewing KYC info of User,Ishaan,Nishant,31-Mar-25,Dev,11-Apr-25,,11,
It1-013,Defect,Lender Portal,Update status should be on verification page only,Medium,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,6-Apr-25,,6,
It1-014,Feature,Lender Portal,"In kyc approval, in the list view, approval date to be there",Low,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,6-Apr-25,,6,
It1-015,Defect,Lender Portal,"Remove email and phone, keep the status, and submittion date in kyc approval in lender module, and review action,, keep name",Low,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,6-Apr-25,,6,
It1-016,Feature,User Onboarding & KYC,"In user module dashboard , split credit line overview into 2 uis, .. 1)  kyc status and 2) credit line",Medium,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,6-Apr-25,,6,
It1-017,Feature,User Onboarding & KYC,"Kyc status and credit line status, do it in under review in the top in terms of sorting (by submission date time) | so credit admin know where to work on",High,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,5-Apr-25,,5,
It1-018,Feature,Admin + Lender Portal,Credit approval submission date in credit assisment  in lender module,Low,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,5-Apr-25,,5,
It1-019,Feature,Admin + Lender Portal,Credit line assignment fields shold have proper validation in lender module | checks on interest rate,Medium,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,12-Apr-25,,12,
It1-020,Feature,Admin,Credit line assignment -> after filling -> show summary as a popo-up -> for confirmation,Low,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,10-Apr-25,,10,
It1-021,Defect,Common,"Active / in active login should work properly, if the user is logged in then it should not logged out in the middle of the process",High,Fixed,,Sanyam,Nishant,31-Mar-25,Dev,9-Apr-25,,9,
It1-022,Feature,Admin,"Risk Profile:  In credit line assignment , add some info on why low , Mediumor high",Low,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,12-Apr-25,To be picked after Lender onboarding - This is completely removed and might be added back after actual lender is onboarded. ,12,
It1-023,Feature,Admin,"In lender module, prooper mapping should be there for lender  | make for 2 lenders for demo (lender2, lender3)",Critical,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,5-Apr-25,,5,
It1-024,Feature,User Dashboard,"Sign contract -> change it to -> ""Accept offer"" in user module in available credit line offers and display KFS before user click on 'ACcept Offer' after which it goes to Sign the Contract. ",Medium,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,11-Apr-25,,11,
It1-025,Feature,User Dashboard,"Show key fact statement in ""Accecpt Offer"" scren and remove unnecessary fields | Take consent again | 3 tick boxes | tnc and accpet offer then e-sign contract",Medium,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,11-Apr-25,,11,
It1-026,Feature,User Dashboard,Add creditl line contract document in Credit Line overview section fo fthe user,Low,Fixed,User Dashboard - Credit Offer Acceptance - Contract Signing,Ishaan,Nishant,31-Mar-25,Dev,6-Apr-25,,6,
It1-027,Feature,User Dashboard,Add 'start discounting now' after the credit line is active (just underneath Credit Line Active) which links to My Invoice Tab,Medium,Fixed,User/ MSME --> Accept Offer ,Aryan,Nishant,31-Mar-25,Dev,24-Apr-25,,24,
It1-028,Defect,Contracts,"After accepting the offer, user should not go back to the previous singing screen | its a bug",Medium,Fixed,,Aryan,Nishant,31-Mar-25,Dev,2-Apr-25,,2,
It1-029,Feature,User Dashboard,"In my invoices , add my credit line | which is not utilize credit lines. | left hand side of upload invoices",Low,Fixed,User Dashboard - My Invoices,Ishaan,Nishant,31-Mar-25,Dev,21-Apr-25,Available Blanance column added ,21,
It1-030,Defect,Invoice,"Right now, we r not checking santity of invoices, user r uploaing same invoice all the time - This is to check nu duplicate invoices exist - based on invoice number match",Medium,Fixed,,Aryan,Nishant,31-Mar-25,Dev,25-Apr-25,OCR taking place but validation of invoices for Iteration 2,25,
It1-031,Feature,Invoice,"In additional document, we have give some instructions right at the top ,, like please uplaod additional document such as xyz, just 2-3 lines at the top, like your contract with buyer abc",Low,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,5-Apr-25,,5,
It1-032,Feature,Buyer Dashboard,"In buyer module, invoices should be sorted by submission date-time",Low,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,5-Apr-25,,5,
It1-033,Feature,Buyer Dashboard,"Add a column, on right side, before status, Submitted At,,,, date -> invoice due date,,,, Supplier -> buyer",Low,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,5-Apr-25,,5,
It1-034,Defect,Buyer Dashboard,"Rather than ""view details"" -> keep ""Review"" ,, be consistent everytwhere",Low,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,6-Apr-25,,6,
It1-035,Defect,Buyer Dashboard,"There is an extra ""Edit"" button, we should be abel to change information without clicking on ""edit"" only for verification invoice scren",Low,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,6-Apr-25,,6,
It1-036,Feature,Buyer Dashboard,"Invoice status dropdown, should enable admin to choose dropdown",Low,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,6-Apr-25,,6,
It1-037,Feature,Lender Portal,"Don't do it in two step, if anchor verified then don't need lender to give submit for discounting (it will go to assigned lender, don't need to submit to msme)  | keep button grayed out | Change status to Anchor verified. then it should show a button there they can click on on apply invoicing ",Medium,Fixed,,Ishaan,Nishant,31-Mar-25,Dev,6-Apr-25,,6,
It1-038,Defect,User Dashboard,"user side, contract not appearing",High,Fixed,User Dashboard - My Invoices,Ishaan,Nishant,31-Mar-25,Dev,5-Apr-25,,5,
It1-039,Defect,Contracts,"Don't show GST, Pan, etc, in User side",Low,Fixed,,Aryan,Nishant,31-Mar-25,Dev,23-Apr-25,,23,
It1-040,Defect,User Dashboard,"Amount -> Invoice Amount,,, availalbe balance -> remove column  user side,",Low,Fixed,USer Dashboard - My Invoices,Ishaan,Nishant,31-Mar-25,Dev,11-Apr-25,,11,
It1-041,Feature,Contracts,"Bank Account details should take in KYC in finacial institution  | INput your bank accocunt details | instruction: This will be used for disbursal and repayments
A new section (Bank Account Details) should be added in Financials Tab at the same level as Financial Documents. Use the format in review and also pass the instruction in small text. Ensure this is stored in userinfo and used for rest of trnsactions such as for dispbrsement and repayment. ",High,Fixed,,Aryan,Nishant,31-Mar-25,Dev,13-Apr-25,Storing in backend is done by Ishaan. Instruction and bank account details added. This was added in one version but removed due to front end. ,13,
It1-042,Feature,Invoice,"In the invice discounting summary screen, add bank account details, Show bank account where the money will be disbursed",Medium,Fixed,User & Onboarding + Contract ,Ishaan,Nishant,31-Mar-25,Dev,10-May-25,,40,
It1-043,Defect,Contracts,"Update loan contract from India Stadnarts to Qatar Standars -> remove inr, pan, etc",Low,Fixed,,Aryan,Nishant,31-Mar-25,Dev,23-Apr-25,,23,
It1-044,Defect,Contracts,"Remove ""For Platform"" and ""Loan Amount"" (after for platform) in loan contract",Low,Fixed,,Aryan,Nishant,31-Mar-25,Dev,16-Apr-25,,16,
It1-045,Defect,Contracts,Last EMI should be correct | Bug,Medium,Fixed,User dashboard -> Accept Invoice Discount offer --> Sign Contract --> Click on PDF copy of Invoice,Aryan,Nishant,31-Mar-25,Dev,16-Apr-25,Iteration 2 checks,16,
It1-046,Feature,Lender Portal,"Add disbursal Tab in Lender module, show disbursal date wise | disbursal details, date, status.",High,Fixed,Go to Lender DAshboard,Ishaan,Nishant,31-Mar-25,Dev,5-Apr-25,,5,
It1-047,Defect,Notifications,No email/ details for buyer and lenders for sending notifications,High,Fixed,,Sanyam,Nishant,31-Mar-25,Dev,4-Apr-25,Lender onbaording and buyer onboarding now has contact details. ,4,
It1-048,Feature,Lender Portal,Status after Active can't be changed back to under review,Medium,Fixed,Lender login --> Go to Active Credit Line Cases --> Change Status,Ishaan,Nishant,01-Apr-25,Dev,5-Apr-25,,4,
It1-049,Defect,Lender Portal,Lender Portal should have same branding (city line backdrop) as User Module,Medium,Fixed,Lender Portal Login Screen,Aryan,Nishant,01-Apr-25,Dev,5-Apr-25,,4,
It1-050,Defect,User Onboarding & KYC,"Validation check for duplicate email or mobile needs to be there. And error message that ""this account exists"" should come up",High,Fixed,User Sign-up/ Login Screen,Aryan,Nishant,02-Apr-25,Dev,9-May-25,Resolution for email and mobile uniquencess done. But combinations for duplicates need to be defined further (iteration 2). ,37,
It1-051,Feature,User Onboarding & KYC,Commercial License Number should be changes to Commercial Registration and move that to CR upload ,Medium,Fixed,User Signup --> Business Tab,Aryan,Nishant,02-Apr-25,Dev,5-Apr-25,,3,
It1-052,Feature,User Onboarding & KYC,"Add new directors in Business Tab of users should allow for edit once I have submitted. For Directors, EKYC notification should be sent. And KYC status against each Directors should be visible. ",Medium,Fixed,User Signup --> Business Tab,Aryan,Nishant,02-Apr-25,Dev,5-Apr-25,,3,
It1-053,Feature,Notifications,Add notification to each Directors for eKYC as per Director email field in business,Medium,Fixed,User Signup --> Business Tab,Ishaan,Nishant,02-Apr-25,Dev,11-Apr-25,,9,
It1-054,Feature,User Onboarding & KYC,Bank account details are not being validated,Medium,New,User Signup --> Financial Tab,Aryan,Nishant,03-Apr-25,Dev,,This will be pushed for Iteration 3 or after integration on bank validation done,76,
It1-055,Feature,User Onboarding & KYC,Email OTP should be dynamic and with the ability to resend OTP after 30seconds - this should be applicable across all OTP screen including contrat signing/ verification,Medium,Fixed,,Aryan,Nishant,03-Apr-25,Dev,5-Apr-25,,2,
It1-056,Feature,Common,A home button should be visible in any screen,Low,Fixed,,Aryan,Nishant,03-Apr-25,Dev,5-Apr-25,,2,
It1-057,Feature,User Onboarding & KYC,User Mobile when logging in from social media login should be manual with user filling in. In next iteration it should fetch. ,Low,Fixed,,Aryan,Abhishek,03-Apr-25,Dev,5-Apr-25,,2,
It1-058,Defect,Common,Width of the box changing as we go from screen to screen,Low,Fixed,,Aryan,Ravindra,03-Apr-25,Dev,5-Apr-25,,2,
It1-059,Feature,User Onboarding & KYC,Business Inelgible flow - phone number should be picked as entered from User Onboarding with drop downdown of the country code. If registered through mobile then no need for resending OTP for registration,Medium,Fixed,Eligibility Module,Aryan,Ravindra,03-Apr-25,Dev,5-Apr-25,,2,
It1-060,Feature,User Onboarding & KYC,Source of Wealth remove,Low,Fixed,,Aryan,Nishant,03-Apr-25,Dev,5-Apr-25,,2,
It1-061,Defect,Lender Portal,Lender View documents not visible,High,Fixed,,Ishaan,Nishant,03-Apr-25,Dev,5-Apr-25,,2,
It1-062,Feature,Lender Portal,"Interest rate change to Annual Percentage Rate (APR) - this should be change on all the offer, KFS and contract screens. ",Medium,Fixed,Lender Credit Line Offer screen,Aryan,Abhishek,03-Apr-25,Dev,22-Apr-25,,19,
It1-063,Feature,Lender Portal,Processing Fee construct for credit line should include flat or % just as invoice,Low,Fixed,Lender offer screen,Ishaan,Nishant,03-Apr-25,Dev,5-Apr-25,,2,
It1-064,Feature,User Dashboard,View RCF/ credit line Contract in the user point of view,Low,Fixed,,Ishaan,Abhishek,03-Apr-25,Dev,5-Apr-25,,2,
It1-065,Defect,Invoice,Additional documents in Invoice - No restriction to move forward. Option add upto 10 documents,Medium,Fixed,,Ishaan,Abhishek,03-Apr-25,Dev,5-Apr-25,,2,
It1-066,Feature,Invoice,Anchor ability to edit invoice should be removed. In terms of rule edit can be done by MSME and Madad Admin but not by Lender and Anchor/ Buyer. ,Low,Fixed,,Ishaan,Abhishek,03-Apr-25,Dev,5-Apr-25,,2,
It1-067,Feature,Invoice,Invoice Filter critieria - Current criteria should be on invoice submission date/ amount/ buyer/ Status  - Submission date should be mentioned rather than just start/ end date,Low,Fixed,,Ishaan,Abhishek,03-Apr-25,Dev,5-Apr-25,Itr 2,2,
It1-068,Feature,Invoice,Invoice Details from lender view needs to show all the doc/ info submitted,Medium,Fixed,Lender Login --> Invoice document ,Ishaan,Abhishek,03-Apr-25,Dev,5-Apr-25,,2,
It1-069,Feature,Lender Portal,Bank account details shoul be updated in Disbursal and EMI excel,High,Fixed,Lender --> Payment Disbursal ,Ishaan,Nishant,03-Apr-25,Dev,24-Apr-25,Disbursal done. EMI not sure required.,21,
It1-070,Defect,Lender Portal,Disbursal Update status is not working as upload not working,High,Fixed,Lender --> Payment Disbursal ,Ishaan,Nishant,03-Apr-25,Dev,5-Apr-25,,2,
It1-071,Feature,Lender Portal,Upload and download file have no bearing on the status. This should be added as part of standard operating procedured,Low,Fixed,Lender --> Payment Disbursal ,Ishaan,Nishant,06-Apr-25,Dev,9-Apr-25,,3,
It1-072,Feature,Lender Portal,Credit Line Application after KYC approval is not coming up in lender dashboard,High,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-073,Defect,User Onboarding & KYC,"Upon singning up with email - post completing the form with valid qatar number, the invalid number error pops up",High,Fixed,Sign-up --> Email --> Enter Mobile --> Complete,Ishaan,Abhishek,07-Apr-25,Dev,9-Apr-25,------> See image ------->,2,
It1-074,Feature,Admin,"Remove Risk Rating from credit assessment screen ie High, Medium, Low",Medium,Fixed,Lender/ Madad Credit Admin --> Credit Assessment,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,This can be brough back after lender onabarding,2,
It1-075,Feature,Admin + Lender Portal,Remove the fee limit absolute number. Processing fee % to be kept at 5%. Calculated fee sentence comes and should be maintained,Medium,Fixed,Lender --> Credit Line Offer Submission ,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-076,Feature,Lender Portal,"Various lender should be able to view/ edit offers only for their logins. This is available till the time borrowers accepts the offer. Once the offer is accepted the lender who are unsuzzeful should not see credit line for the customer - should be greyed out and status shoud change to ""Your Offer Status: Elapased"" ",High,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-077,Feature,Admin & User Dashboard,Tick boxes for credit line contract signing should have all three boxes just as invoice disoucinting contract module,Medium,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-078,Feature,User Dashboard,View Contract should be changes to View Credit Line Contract,Low,Fixed,User/. MSME Dashboard --> Ater Credit Line Contract is signed,Ishaan,Nishant,07-Apr-25,Dev,14-Apr-25,,7,
It1-079,Feature,Contracts,"Credit Line lender specific contract should be pre-filled with variables (such as lender name, borrower name etc) with name of the borrrower on last page as sign with date/ time stamp and IP address of the borrower. ",High,Fixed,,Aryan,`,07-Apr-25,Dev,16-May-25,Currently this will be hard coded template. To be done in Iteration 2,39,
It1-080,Feature,Lender Portal,Lender Branding and Logo should be place next to the profile icon,Low,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-081,Feature,Common,Profile icon shouild be linked for each user - MSME/ Madad/ Lender to the Profile.  It shold be annotated with Welcome <Your name>,Low,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-082,Defect,Buyer Dashboard,Change Supplier column name to Buyer,Low,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-083,Feature,Buyer Dashboard,"Change Customer Name to ""Trade/ Legal Entity Name"" on the dashboard.",Low,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-084,Defect,Invoice,REview Invoice Customer Name should be updated to User/ MSME Name,Low,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-085,Feature,Common,Verification of Invoice Screenand Invoice Discounting Screen sizing issue - should be responsive. Couldnt see verifiy button till i reduced zoom to 85%. ,Medium,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-086,Feature,Invoice,Add/ Edit Comments should be editable on verify invoices button,Medium,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-087,Feature,User Dashboard,"Once invoice verified, the lender invoice dashboard should see Action saying ""Approve Discounting"" and otherwise ""Details""",Low,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-088,Feature,Contracts,Invoice Offer acceptance of contract leads to blank/ white screen which is populated on reload,Medium,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-089,Feature,Contracts,"Invoice Contract TEmplate should beupdated to pick variable field such as Username/ Business address, Rupee to change to AER,Lender name update ",High,Fixed,,Ishaan,Nishant,09-Apr-25,Dev,9-Apr-25,,0,
It1-090,Feature,Contracts,Calculation for EMI/ processing fee etc for invoice is not correct ,High,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-091,Feature,User Dashboard,"Onc invoice discounting contract signed the My invcoics dashboard should show ""View Invoice Discounted Contract"" in the Action button",Medium,Fixed,,Ishaan,Nishant,07-Apr-25,Dev,9-Apr-25,,2,
It1-092,Feature,Common,"Login Session should time out after 30mins i.e. in case of no activity for 30mins, user should be logged out - make it for MSME, Buyer, Admin, Lender",Medium,In Progress,,Sanyam,Nishant,09-Apr-25,Dev,,Awaiting for production release,70,
It1-093,Feature,User Dashboard,Profile link from drop down menu,Medium,Fixed,,Aryan,Nishant,09-Apr-25,Dev,11-Apr-25,,2,
It1-094,Feature,User Dashboard,Links to Eligbility and Dashboard should be added,Medium,Fixed,,Aryan,Nishant,09-Apr-25,Dev,11-Apr-25,,2,
It1-095,Feature,User Dashboard,"Shareholder Screen should have email address and store in db, also send the email to those emails before KYC finishes",High,Fixed,,Ishaan,Nishant,09-Apr-25,Dev,15-Apr-25,,6,
It1-096,Defect,User Dashboard,Check unique shareholder through combination of sharehgolder name  +email address,High,Fixed,,Ishaan,Nishant,09-Apr-25,Dev,21-Apr-25,,12,
It1-097,Defect,User Dashboard,"Buyer bulk upload not working. Response backend not saving buyers properly, also not showing in the frontend after saving.",Medium,Fixed,User Dashboard --> Bulk Upload,Aryan,Sanyam,09-Apr-25,Dev,11-Apr-25,,2,
It1-098,Feature,User Onboarding & KYC,Missing information highlight in review page should be highlighted through 'red' or missing x of y message,Medium,Fixed,,Aryan,Sanyam,09-Apr-25,Dev,9-May-25,Missing x of y in Review section incorporated,30,
It1-099,Defect,User Onboarding & KYC,"Can edit all the components, Buyer information missing on review page",Medium,Fixed,Not able to edit all the components,Aryan,Sanyam,09-Apr-25,Dev,11-Apr-25,,2,
It1-100,Feature,Admin,KYC status will be modified by Madad Team on the admin portal using drop down menu,Medium,Fixed,Admin --> Review Sharegholder to Change Status,Ishaan,Sanyam,09-Apr-25,Dev,15-Apr-25,This will be done on iteration 2,6,
It1-101,Feature,Admin,Not able to view merchant document,High,Fixed,,Aryan,Sanyam,09-Apr-25,Dev,15-Apr-25,,6,
It1-102,Feature,Admin,"Madad admin can flag the unverified document by adding as a document by ""Add Documents"" with comments of deficiency",Low,Fixed,,Ishaan,Sanyam,09-Apr-25,Dev,15-Apr-25,,6,
It1-103,Feature,Admin,Each document verification schema should be matained with approve or reject buttons,High,Fixed,,Ishaan,Sanyam,09-Apr-25,Dev,15-Apr-25,,6,
It1-104,Feature,Admin,"Unique 5/6 digit alphanumeric number to be mapped with the merchant, buyer, lender",High,Fixed,,Sanyam,Sanyam,09-Apr-25,Dev,13-May-25,"This will be done on iteration 2, 6 digit alphnumeric id store in UserInfo",34,
It1-105,Feature,User Dashboard,"Not able to accept the offer from activation journey | As soon as the offer is accepted the button changes to sign contract ot htat offer. Rest of the buttons are disabled for other offers, on clicking sign contract navigate to contract signing, once a credit offer has been accepted change the button to view contract and disabled all button for other offer but still show other offer with other made. Refer wireframe ",High,Fixed,,Aryan,Sanyam,09-Apr-25,Dev,15-Apr-25,,6,
It1-106,Feature,User Dashboard,Update financial data and credit berau data text everywhere,Low,Fixed,,Ishaan,Sanyam,09-Apr-25,Dev,15-Apr-25,,6,
It1-107,Feature,User Dashboard & Buyer Dashboard,"For Invoice more information from Buyer then required then User action should be ""edit"" and should be able to upload more information as additional document - though invoice editing should not be enabled",Medium,Fixed,,Ishaan,Sanyam,09-Apr-25,Dev,15-Apr-25,,6,
It1-108,Feature,Buyer Dashboard,Change Anchor to Buyer across all including DAshboard/ Status,High,Fixed,,Ishaan,Sanyam,09-Apr-25,Dev,15-Apr-25,,6,
It1-109,Feature,Common,Buyer and Lenderscreen invoice submission sorting should show invoice submitted date,Medium,Fixed,,Aryan,Sanyam,09-Apr-25,Dev,11-Apr-25,Tailwind filter adopted,2,
It1-110,Feature,Lender Portal,Interest rate on Invoice Discouting KFS is not visible when Lender choses invoice for discounting,Low,Fixed,,Ishaan,Sanyam,09-Apr-25,Dev,24-Apr-25,,15,
It1-111,Feature,User Dashboard,Customise Template for Qatar/ AER/ Variable name ,High,Fixed,,Aryan,Nishant,09-Apr-25,Dev,17-Apr-25,,8,
It1-112,Feature,User Onboarding & KYC,"navigation kyc journey, 3 documents in 2nd step not saving properly",Medium,Fixed,,Aryan,Ishaan,7-Apr-25,Dev,11-Apr-25,,4,
It1-113,Feature,User Onboarding & KYC,Kyc tabs can be viewed and edited without click on save and continue and switch the order of buttons in kyc screen and removed notification icons,High,Fixed,User Onboarding - Review,Aryan,Ishaan,09-Apr-25,Dev,11-Apr-25,,2,
It1-114,Feature,Lender Portal,"Lender wireframe devellopemment -> change 2 screens -> refer wireframe -> 1. kyc to credit application, 2. ",High,Fixed,,Aryan,Nishant,12-Apr-25,Dev,14-Apr-25,,2,
It1-115,Feature,Buyer Dashboard,Login username for buyer should be changed to buyeradmin,Low,Fixed,,Aryan,Nishant,13-Apr-25,Dev,24-Apr-25,username changed to buyeradmin and all the roles also changed to buyeradmin,11,
It1-116,Feature,User Onboarding & KYC,OCR should be enabled for all KYC documents. Standard definition of what to compare it against will be be provided. ,High,Fixed,,Ishaan,Nishant,13-Apr-25,Dev,17-Apr-25,Enabled for all documents. Rest of the consistency will be tracked through IT-129,4,
It1-117,Feature,User Onboarding & KYC,Reimplement OCR and data extraction for Financial Statements,Medium,Fixed,,Ishaan,Nishant,13-Apr-25,Dev,15-Apr-25,,2,
It1-118,Feature,User Onboarding & KYC,Video KYC should be included even on offline basis for iteration 1. This will be done by Shufti. ,Medium,Fixed,,Ishaan,Nishant,13-Apr-25,Dev,6-May-25,Candidate for Iteration 2,23,
It1-119,Feature,User Onboarding & KYC,The icon for shareholder kyc for approved status need to be changed,Medium,Fixed,,Ishaan,Sanyam,13-Apr-25,Dev,26-Apr-25,,13,
It1-120,Feature,Common,Higlight the current tab in sidebar,Medium,Fixed,,Aryan,Sanyam,13-Apr-25,Dev,14-Apr-25,,1,
It1-121,Feature,Lender Portal,Not able to view documents in lender dashboard,Medium,Fixed,,Aryan,Sanyam,13-Apr-25,Dev,14-Apr-25,,1,
It1-122,Defect,Lender Portal,Top buyer format should be applied as same as msme,Medium,Fixed,,Aryan,Sanyam,13-Apr-25,Dev,14-Apr-25,,1,
It1-123,Feature,Lender Portal,"Credit line status cases should include ""more information required"" for the lenders",High,Fixed,,Ishaan,Nishant,13-Apr-25,Dev,23-Apr-25,Same as It-138,10,
It1-124,Feature,User Dashboard,After activation restrict profile edit,Medium,Fixed,,Ishaan,Sanyam,13-Apr-25,Dev,15-Apr-25,,2,
It1-125,Feature,Common,Maintain the sheet at all points,Medium,Fixed,,Nishant,Abhishek,16-Apr-25,Dev,24-Apr-25,,8,
It1-126,Feature,User Onboarding & KYC,Qatar flag at login,Low,Fixed,"Not able to reproduce in my system, all flags are working fine here - Aryan",Aryan,Sanyam,13-Apr-25,Dev,21-Apr-25,,8,
It1-127,Defect,Common,Bigger font size all over,Low,Fixed,,Aryan,Sanyam,13-Apr-25,Dev,11-May-25,,28,
It1-128,Feature,Common,Qatar SMS.net Integration,High,Fixed,,Ishaan,Sanyam,13-Apr-25,Dev,24-Apr-25,This is implemented for Qatar,11,
It1-129,Feature,Common,OCR for all uploaded documents,High,Fixed,,Ishaan,Sanyam,13-Apr-25,Dev,19-Apr-25,,6,
It1-130,Feature,User Dashboard,Preview in KYC section,Low,Fixed,,Aryan,Sanyam,13-Apr-25,Dev,15-Apr-25,Removed,2,
It1-131,Feature,User Dashboard,"Additional landmark rename it to email Id and remove email id. On first tab keep First Name, Middle Name and Last Name only. ",Medium,Fixed,User KYC --> Shareholder details (consult Sanyam),Aryan,Sanyam,13-Apr-25,Dev,15-Apr-25,,2,
It1-132,Defect,User Dashboard,Email + name check should be there for shareholders/ unique shareholders,Medium,Fixed,User KYC --> Shareholder details (consult Sanyam),Sanyam,Sanyam,13-Apr-25,Dev,19-Apr-25,,6,
It1-133,Feature,User Dashboard,Video KYC options as part of Ekyc as per shared by abhishek should be done for both shareholder + An email to be sent to user which will be redirected to the msme portal + To be shown to madad for verification on portal,Medium,Fixed,,Ishaan,Sanyam,13-Apr-25,Dev,6-May-25,Iteration 2 candidate,23,
It1-134,Feature,Common,Icon according to status needs to be changed everywhere. For eg no clock button once status is done. ,Low,Fixed,,Ishaan,Sanyam,13-Apr-25,Dev,26-Apr-25,,13,
It1-135,Feature,Admin,KYC document verifications need to show which document is upload again as per request ( matain the history). Maintain the historical logs of document uploaded/ replaced. Show it in the frontend that this documents was updated. ,Medium,Fixed,,Ishaan,Sanyam,13-Apr-25,Dev,10-May-25,,27,
It1-136,Feature,Lender Portal,Ability to download the all the KYC details of msme in a single document as a Zip rather than append to same file,Medium,Fixed,,Aryan,Nishant,13-Apr-25,Dev,22-Apr-25,,9,
It1-137,Feature,Lender Portal,"Over all KYC to be EKYC, option to view Video KYC as well (dependednt on It-133)",Medium,Duplicate,,Ishaan,Nishant,13-Apr-25,Dev,,Iteration 2 candidate,66,
It1-138,Feature,Lender Portal,Lender should have option to put - MORE INFORMATION REQUIRED status - as they do KYC confirmation and then credit assessment,High,Fixed,,Ishaan,Nishant,13-Apr-25,Dev,23-Apr-25,Currently lender can only accept Madad approved KYC. If they need more doc as part of credit assessment it will be considered as part of credit approval stage,10,
It1-139,Feature,User Onboarding & KYC,"Review page of User should mimic the tabs - Business/ Financials/ Shareholder/ Buyer - use the same terminolgy eg Top Buyer = Top Buyer not Buyer Details. Only section extra is ""uploaded documents""",High,Fixed,,Aryan,Nishant,14-Apr-25,Dev,14-Apr-25,Inconcistent terms between Review and Main tabs in line with requrest in wireframe. ,0,
It1-140,Feature,User Onboarding & KYC,Submission page has Tick Box and three statement that are repeat.Just keep Tick box statements with respective link. ,Low,Fixed,,Aryan,Nishant,14-Apr-25,Dev,14-Apr-25,,0,
It1-141,Feature,Admin,"Make a rule that unless all documents are marked as verified and all the shareholders are approved, the admin can't change the MSME KYC status to ""Approved""",High,Fixed,,Ishaan,Nishant,14-Apr-25,Dev,21-Apr-25,This will be in Iteration 2,7,
It1-142,Feature,Admin,Once the KYC is approved the onboarding section is locked and user can only view that information cannot not change and resubmitted; except if KYC is not approved and in other status such More info required. The same information of KYC/ onboarding can also be accessed through Profile section,High,Fixed,,Ishaan,Nishant,14-Apr-25,Dev,15-Apr-25,,1,
It1-143,Feature,User Dashboard,"Credit Analytics & DAshboard should have latest status of the Credit line satus with prompt to complete the action. For eg. if the offer has been given by a lender, then landing page should have Credit Line Status as ""under review"" with action ""Review and Accept Offer"" with a link to Activation Tab to complete the journey. ",Medium,Fixed,,Aryan,Nishant,14-Apr-25,Dev,22-Apr-25,,8,
It1-144,Feature,User Dashboard,Change Key Fact Statement pop on Credit Line Accept Offer and Contract to Annual Percentage Rate (APR) - make it consistent with other screen/ text for eg in Lender Screen ,Low,Fixed,,Aryan,Nishant,14-Apr-25,Dev,7-May-25,,23,
It1-145,Feature,User Dashboard,Pick IBAN Details when extracting banking statement,Medium,Fixed,,Ishaan,Suresh,15-Apr-25,Dev,21-Apr-25,,6,
It1-146,Feature,User Dashboard,Can we give details of the document name in a shortened easy to ready manner when asking user/ MSME to resubmit the failed doc (10-15 meaninful characters in the whole name with extension i.e. .pdf etc -> only for review page,Low,Fixed,User Dashboard --> Review Page --> Uploaded doc name,Aryan,Ravindra,15-Apr-25,Dev,13-May-25,Iteration 2,28,
It1-147,Feature,User Dashboard,"Accepted should be changed to ""Another offer accepted""",Medium,Fixed,,Ishaan,Nishant,15-Apr-25,Dev,21-Apr-25,,6,
It1-148,Feature,User Dashboard,Add Finance and Data Consent Link to the invoice upload submission ,Low,Fixed,,Aryan,Nishant,15-Apr-25,Dev,20-Apr-25,,5,
It1-149,Defect,Invoice,Change AER instead of rupee in the invoice loan contract,Medium,Fixed,,Aryan,Nishant,15-Apr-25,Dev,23-Apr-25,,8,
It1-150,Feature,Lender Portal,Bank A/C Disbursal Details for the MSMEs in the download template should be there and filled automatically,Medium,Fixed,,Ishaan,Nishant,15-Apr-25,Dev,24-Apr-25,,9,
It1-151,Defect,User Onboarding & KYC,Google id is displayed in the KYC details of the user,Low,Fixed,,Aryan,Abhishek,20-Apr-25,Dev,23-Apr-25,Removed for iteration. Will be variable based from eligibiity checker.,3,
It1-152,Feature,Admin,KYC section in madad portal is sorted by date with. earlier date on top- latest date should be first,Low,Fixed,,Ishaan,Abhishek,20-Apr-25,Dev,21-Apr-25,Under Review + Submitted Date-Time sorting is applied,1,
It1-153,Defect,User Onboarding & KYC,"Duplicate buyers can created in the system - repeatitive entry should be restricted in any such form , for example in the shareholder section as well",High,Fixed,,Ishaan,Abhishek,20-Apr-25,Dev,22-Apr-25,Combination of First Name and Emal check for Uniqueness,2,
It1-154,Feature,User Onboarding & KYC,"Not able to sign-up using phone number or email id, only Google API is working [P0]",High,Fixed,,Aryan,Ravindra,20-Apr-25,Dev,21-Apr-25,,1,
It1-155,Defect,User Onboarding & KYC,Sign-up with email is not working,Medium,Fixed,,Aryan,Ravindra,20-Apr-25,Dev,21-Apr-25,Checked login using Gmail and separate email ID - it seems to be working in both case. Need further replication/ testing by user,1,
It1-156,Feature,Common,State management missing - all documents are uploaded but not saved,High,Fixed,,Sanyam,Ravindra,20-Apr-25,Dev,21-Apr-25,,1,
It1-157,Defect,User Onboarding & KYC,‘Save & Continue’ button is not working - inconsistent behaviour,High,Fixed,User login through email --> Upload doc --> Logout and login --> Saved doc not seen,Ishaan,Ravindra,20-Apr-25,Dev,21-Apr-25,checked and seems to be working in most scenario. Needs replication by user. ,1,
It1-158,Feature,Common,GCP is still in South Asia [P0],High,Fixed,,Sanyam,Ravindra,20-Apr-25,Dev,24-Apr-25,"UAT is in Qatar, Dev in Asia. ",4,
It1-159,Feature,Common,"It is not “Madad Financial Technology”, it is “Madad Financial Technologies” [P0]",Low,Fixed,,Aryan,Ravindra,20-Apr-25,Dev,22-Apr-25,,2,
It1-160,Feature,User Onboarding & KYC,Eligibility check - Drop down for Turnover and number of employees should be as per the breaks of MSME definition [P0],Medium,Fixed,User/MSME Registration -->Eligibility Module,Ishaan,Ravindra,20-Apr-25,Dev,21-Apr-25,,1,
It1-161,Feature,User Onboarding & KYC,Business Documents - MOA and AOA should not be mandatory [P0],Low,Fixed,,Ishaan,Ravindra,20-Apr-25,Dev,22-Apr-25,,2,
It1-162,Feature,User Onboarding & KYC,OCR not working in the review tab. All fields are “Not provided” [P0],Medium,Fixed,,Ishaan,Ravindra,20-Apr-25,Dev,24-Apr-25,,4,
It1-163,Feature,Common,"Upload T&C, privacy policy etc. as per Madad business [P0]",Medium,Fixed,,Ishaan,Ravindra,20-Apr-25,Dev,23-Apr-25,,3,
It1-164,Feature,User Onboarding & KYC,"Document rejected by Madad Admin, but not visible to MSME in the portal [P0]. Add document name in the portal",Low,Fixed,,Ishaan,Ravindra,20-Apr-25,Dev,24-Apr-25,,4,
It1-165,Feature,Notifications,"Email sent post sign up - Sender, Subject, and email body needs to change [P1]",Medium,Fixed,,Sanyam,Ravindra,20-Apr-25,Dev,24-Apr-25,,4,
It1-166,Feature,User Onboarding & KYC,CR number can have basic check e.g. number of digits [P1],Medium,Fixed,,Ishaan,Ravindra,20-Apr-25,Dev,21-Apr-25,Added a check for CR number to be 5 or 6 or 7 digits only,1,
It1-167,Feature,User Onboarding & KYC,Eligibility check logic needs to be redefined [P1],Medium,Fixed,,Abhishek,Ravindra,20-Apr-25,Dev,11-May-25,Will be picked for iteration 3 and need to be defined. ,59,
It1-168,Feature,Notifications,If we are sending an email on each document submission why do we need Save and Continue to save all documents in a tab [P1],Low,Fixed,,Ishaan,Ravindra,20-Apr-25,Dev,24-Apr-25,,4,
It1-169,Defect,Admin,"Admin Portal No passwords needed to access portal, why? [P0]",High,Fixed,,Sanyam,Ravindra,20-Apr-25,Dev,25-Apr-25,,5,
It1-170,Defect,Admin,Why there is no download button for Madad admin? [P0],Medium,Fixed,,Ishaan,Ravindra,20-Apr-25,Dev,22-Apr-25,Download all documents as a zip,2,
It1-171,Defect,Lender Portal,Lender portal is accessible without password [P0],High,Fixed,,Sanyam,Ravindra,20-Apr-25,Dev,25-Apr-25,,5,
It1-172,Defect,Lender Portal,Lender is not able to see any of the uploaded documents or information entered in the KYC stage [P0],Medium,Fixed,,Ishaan,Ravindra,20-Apr-25,Dev,24-Apr-25,,4,
It1-173,Feature,User Onboarding & KYC,Bank Account and IBAN/ IFSC/ SWIFT should be mandatory. Mark it as asterisk on front end.,Medium,Fixed,,Ishaan,Nishant,21-Apr-25,Dev,20-May-25,,58,
It1-174,Feature,User Onboarding & KYC,Take T&C and Privacy Policy after eligibility check is correct,Medium,Duplicate,,Ishaan,Nishant,21-Apr-25,Dev,,Duplicate of it-179,58,
It1-175,Defect,Admin,Add Madad Admin UserName just like other behaviour,Low,Fixed,,Ishaan,Nishant,21-Apr-25,Dev,23-Apr-25,,2,
It1-176,Feature,Lender Portal,Credit Assessment change to Credit Applications,Low,Fixed,,Ishaan,Nishant,21-Apr-25,Dev,21-Apr-25,,0,
It1-177,Defect,User Onboarding & KYC,"Review page for MSME borrower has Qatar based as ""No"". Seems an error. ",Low,Fixed,,Ishaan,Abhishek,21-Apr-25,Dev,23-Apr-25,REmoved based in Qatar all together. This will added as variable from elgibility criteria going forward. ,2,
It1-178,Feature,User Onboarding & KYC,Email in review page for shareholder is not visible,Low,Fixed,,Ishaan,Abhishek,21-Apr-25,Dev,23-Apr-25,,2,
It1-179,Feature,User Onboarding & KYC,"All the three tick boxes for T&C, Consent, Credit Bureau data should be added in Eligibility Criteria for approved customer",Medium,Fixed,,Ishaan,Ravindra,21-Apr-25,Dev,23-Apr-25,,2,
It1-180,Feature,User Onboarding & KYC,"After submission of KYC application, the default screen should be redirected to activation journey",Low,Fixed,,Ishaan,Abhishek,21-Apr-25,Dev,10-May-25,,19,
It1-181,Feature,Admin,KYC review filters to be added,Low,Fixed,,Ishaan,Abhishek,21-Apr-25,Dev,26-Apr-25,,5,
It1-182,Feature,Admin,KYC Review tab --> Review other info should be changed to EKYC and Summary. Under EKYC - vendor summary will come + all the summarised infromation,Medium,Fixed,,Ishaan,Abhishek,21-Apr-25,Dev,22-Apr-25,,1,
It1-183,Feature,Admin,"Shareholder KYC should be manually changed to approved - this provisions should be provided -  before overall KYC status is allowed to be changed to approved. Also change the status button to ""EKYC Status"".",Medium,Fixed,,Ishaan,Nishant,21-Apr-25,Dev,22-Apr-25,Manual update of shareholder KYC required. Added rule to not let KYC approved before shareholders KYC is manually updated.,1,
It1-184,Feature,Lender Portal,Pending KYC should be removed from dashboard as lender is not responsible for KYC,Medium,Fixed,,Ishaan,Nishant,21-Apr-25,Dev,22-Apr-25,,1,
It1-185,Feature,Lender Portal,"Analytics and Report should be updated with actual formula. Eg Invoice, User",High,Duplicate,,Nishant,Nishant,21-Apr-25,Dev,,Handled through different reporting tickets,58,
It1-186,Feature,Admin + Lender Portal,KYC - Personal Detils - remove things that are not asked for eg home/ vehicle etc. ,Medium,Fixed,Lender Portal and Madad Admin Portal,Ishaan,Nishant,21-Apr-25,Dev,12-May-25,,21,
It1-187,Feature,Lender Portal,"Credit Applicaitons review for each use case 
Business Document tab shouldnt have Invoiuce ADditonal Doc1, 2 till we get the invoice flow
Financial Documents - remove CCR upload to be removed 
Check document status Submitted when nothing was uploaded - eg Audited Financial Report
Shareholder - no shareholders shown which is not as per submitted form
Remove 'Directories & Signatories' 
Top Buyer - Payment terms was never asked - remove",Medium,Fixed,Lender Portal - Credit Applicaitons --> Individual Case,Ishaan,Abhishek,21-Apr-25,Dev,23-Apr-25,,2,
It1-188,Feature,Lender Portal,More information required during credit applicaiton stage need to be provided as a separate status,Medium,Fixed,Lender Portal - Credit Applicaitons --> Individual Case Approval,Ishaan,Nishant,21-Apr-25,Dev,12-May-25,,21,
It1-189,Feature,Notifications,Credit Line Created should be changed to Credit Line Activated,Low,Fixed,Lender Notifications,Ishaan,Abhishek,22-Apr-25,Dev,12-May-25,,20,
It1-190,Feature,Common,"Password policy: Enforces 12-character complex passwords with 3-month renewal. So enforce 12 word with messages at registration/ login saying password is 12-character inlcuding numbers, letters, special characters. Extend it to lender, buyer, madad admin etc",High,Fixed,Common,Sanyam,Nishant,22-Apr-25,Dev,6-May-25,,14,
It1-191,Feature,Admin ,"Admin Portal should have all the Tabs - KYC, Credit Applicaiton, Credit Line, Invoice Discounting, Payments (with noth disbursal and repayment tab)",High,Fixed,Credit Admin --> Tabs/ functionalities on left,Ishaan,Nishant,22-Apr-25,Dev,12-May-25,,20,
It1-192,Feature,Lender Portal,download all docs button for Lender portal,Medium,Fixed,,Ishaan,Nishant,22-Apr-25,Dev,25-Apr-25,,3,
It1-193,Feature,Lender Portal,System & Audit Info should be removed,Medium,Fixed,,Ishaan,Nishant,22-Apr-25,Dev,25-Apr-25,,3,
It1-194,Feature,User Dashboard,Removed Promotions & Offer,Low,Fixed,,Ishaan,Nishant,22-Apr-25,Dev,25-Apr-25,,3,
It1-195,Defect,Buyer Dashboard,Change Supplier to buyer,Low,Fixed,,Aryan,Abhishek,22-Apr-25,Dev,25-Apr-25,,3,
It1-196,Defect,User Onboarding & KYC,Change Commercial Bank Logo in Elgibility to Shariah compliant bank logo,Low,Fixed,,Aryan,Ravindra,23-Apr-25,UAT,27-Apr-25,,4,
It1-197,Defect,User Dashboard,Without lender activation if buyer goes to invoice section he gets incorrect message 'Your credit line is approved. Please accept the offer to activate it.',Low,Fixed,,Ishaan,Abhishek,24-Apr-25,UAT,7-May-25,,13,
It1-198,Defect,Lender Portal,Lender unable to discount invoice if two invoices are added one after the other ,Medium,Fixed,,Ishaan,Abhishek,24-Apr-25,UAT,5-May-25,Invalid doc. Couldn't replicate the invoice. ,11,
It1-199,Feature,User Dashboard,Remove interest field from the offer in the marketplace offer. ,Medium,Fixed,,Nishant,Nishant,24-Apr-25,UAT,27-Apr-25,Broader discussion on Shariah compliance. For interim this is interest rate is changes to service fee.,3,
It1-200,Defect,Lender Portal,"Once lender has loan In Progress it can no longer see the contract of the invoice discounted or the KYC details of the user. There should be an option to see/ download KYC, contract and EMI schedule in the screen",Medium,Fixed,,Ishaan,Nishant,24-Apr-25,UAT,22-May-25,For iteration 2,55,
It1-201,Feature,Lender Portal,"Use actual Islamic bank names - Change Citibank and UK bank with actual Shariah compliant banks in Qatar with associated Logo. Following placeholder banks to be used 
QIB (lender1)
Commercial Bank of Qatar (CBQ) https://www.cbq.qa/en lender2
Alrayan Bank https://www.alrayan.com/en lender3",Medium,Fixed,,Ishaan,Rashid,24-Apr-25,UAT,27-Apr-25,,3,
It1-202,Feature,User Onboarding & KYC,Whitelisting of the Buyer - assume top buyer are all whitelisted and uploaded invoices are checked against whitelisted buyer,High,Fixed,,Ishaan,Nishant,29-Apr-25,Dev,15-May-25,"If buyer name matches the one the msme gave us: let them proceed. If not, they can proceed but should have provision to add buyer as a new one. Issue: the msme can just add the buyer in the profile section/edit the name of an existing one in that case to match the one they want to upload Nishant Bhaskar",16,
It1-203,Feature,Invoice,Auto check invoice against whitelisted buyers,Medium,Fixed,,Ishaan,Nishant,29-Apr-25,Dev,12-May-25,,13,
It1-204,Feature,User Onboarding & KYC,"Implement the following using shufti pro: AML screening, document verification, KYC, KYB (enhanced), address verification",High,Fixed,,Ishaan,Abhishek,29-Apr-25,UAT,2-Jun-25,Address verification and geo-tagging through a different ticket. Rest all is implmented.,50,
It1-205,Feature,Invoice,Invoice validation - duplicate invoice on Madad platform,High,Fixed,,Aryan,Abhishek,01-May-25,Dev,13-May-25,,12,
It1-206,Defect,User Onboarding & KYC,Change Bank Statement to Business Bank Statement,Low,Fixed,,Ishaan,Nishant,02-May-25,UAT,7-May-25,To be done as part of KYC integration,5,
It1-207,Feature,Credit Risk,"Make a CAM File/ Credit Assessment Report for MSME that will be shared with FIs. Have Six sections in the commercial downloadable PDF Report (Five as per below + One Credit Summary & Guidance). Most of the docs should be https links to GCP storage. 
 
- All KYC & KYB Summary Documents Link view taken from Onboarding/ KYC Review
- Key Extract/ Summary of Bank Statement
- Key Extract Summary of Cash flow statement

This report is generated after KYC of MSME is approved taking respective fields from collections and is made available for FI and Admin. ",High,Fixed,Madad Admin/ Lender Portal -->Credit Offer,Aryan,Nishant,01-May-25,Dev,5-Jun-25,"add monthly average balance




",48,
It1-208,Defect,User Onboarding & KYC,"Check if the uploaded document is the intended document. 
For each of the uploaded doc, create a reudimentary/ common key word (or other regex) based based check whether doc uploaded is the right one. for eg resume uploaded in place of CR doc should be picked!!
",Medium,Fixed,Common,Aryan,Nishant,02-May-25,Dev,5-Jun-25,Using GeminiAI to check authenticity,47,
It1-209,Feature,User Onboarding & KYC,Auto-fill information based on submissions at each step,Medium,Fixed,,Ishaan,Nishant,02-May-25,Dev,12-May-25,Except for business address this is done,10,
It1-210,Feature,User Onboarding & KYC,Functionality to self e-mail the submitted application with the documents. Mark the submission email as Restricted. ,Low,In Progress,,Aryan,Nishant,02-May-25,Dev,,,47,
It1-211,Feature,User Onboarding & KYC,"MSME wise view on the completion of the application whereas completion means KYC approved which itself is broken as document verified, shufti KYC verified, shareholder KYC verfified. This status should come under overall verification pending status within ACtivation Journey. ",Medium,Fixed,User Onboarding --> Activation --> Status of Onboarding,Ishaan,Nishant,02-May-25,Dev,12-May-25,,10,
It1-212,Feature,User Onboarding & KYC,Activity log at MSME level to capture any activity performed by/for the MSME,Medium,Fixed,,Ishaan,Nishant,02-May-25,Dev,12-May-25,,10,
It1-213,Feature,User Onboarding & KYC,Auto document verification status,Medium,Fixed,,Ishaan,Nishant,02-May-25,Dev,8-May-25,Implemented for CR and Bank Statement,6,
It1-214,Feature,Credit Risk,CAM file Shared with FIs and Admin in the credit applications and credit offer tab. ,High,Fixed,,Aryan,Nishant,02-May-25,Dev,14-May-25,CAM report view appearing in Lender and Admin Portal,12,
It1-215,Feature,Credit Risk,Credit offers received and accepted status at MSME level,Medium,Fixed,,Aryan,Nishant,02-May-25,Dev,9-May-25,,7,
It1-216,Feature,Contracts,"Provision to upload physically signed contract for 1. credit line contract and 2. invoice loan contract. Add a field/ provision to ""Physically Sign, Scan and Upload Contract"" next to digital contract copy. No restriction in terms of customer journey. In the backend for lender/ madad admin to activate or disburse will have handled offline. ",Medium,In Progress,MSME Dashboard --> Credit Line Contract Screen + Invoice Contract Screen,Aryan,Nishant,02-May-25,Dev,,,47,
It1-217,Feature,Invoice,Ability for MSME to add new buyers during the time of invoice submission,Medium,Fixed,,Ishaan,Nishant,02-May-25,Dev,12-May-25,,10,
It1-217,Feature,User Onboarding & KYC,"Add VideoKYC, Top Buyers and Shareholder KYC status in respoective Review Screen",Medium,Fixed,,Ishaan,Nishant,02-May-25,Dev,7-May-25,,5,
It1-218,Feature,Invoice,Dropdown of (Top/ whitelisted) buyers again invoices for MSMEs. Do this at the stage of Invoice upload and OCR scan. ,Medium,Fixed,,Ishaan,Nishant,02-May-25,Dev,12-May-25,,10,
It1-218,Feature,Admin,View/Edit access of invoice submitted for discounting. ,Medium,Fixed,Admin --> Invoice Approval,Ishaan,Nishant,02-May-25,Dev,7-May-25,This can be done only till invoice is not discounted. once discounting is done Invoice can't be edited. ,5,
It1-219,Feature,Notifications,"Communication back to the user at specific changes in application process (when, format, channel, frequency etc.) with content on changes made ",Medium,In Progress,,Sanyam,Nishant,02-May-25,Dev,,Notification Matrix,47,
It1-219,Feature,Lender Portal,Status of contract signed by MSME for invoice discounting. For invoices with status of disbursed - corresponding invoice term loan contract should be availavble to view,Medium,Fixed,Lender Portal --> Invoice Discounting --> Invoice Table Action column,Ishaan,Nishant,02-May-25,Dev,10-May-25,,8,
It1-220,Feature,Invoice,Status against each invoices,Medium,Fixed,,Ishaan,Nishant,02-May-25,Dev,7-May-25,,5,
It1-220,Feature,Notifications,Updated notification content to include notification specific actions,Medium,In Progress,,Sanyam,Nishant,02-May-25,Dev,,Notification Matrix,47,
It1-221,Feature,Notifications,Reminder to MSME about Due date of EMIs and Annual KYC refresh,Medium,In Progress,,Sanyam,Nishant,02-May-25,Dev,,Notification Matrix,47,
It1-221,Feature,Admin ,"Summary view - Agregate on Whitelisted Buyer, Credit Line ",Medium,In Progress,,Ishaan,Nishant,02-May-25,Dev,,,47,
It1-222,Feature,Notifications,Notification on successful disbursal from FIs/Banks against an invoice,Medium,In Progress,,Sanyam,Nishant,02-May-25,Dev,,Notification Matrix,47,
It1-222,Feature,Lender Portal,"Summary view - Agregate on MSME, FI, Whitelisted Buyer and Invoices Dicounted",Medium,In Progress,,Sanyam,Nishant,02-May-25,Dev,,,47,
It1-223,Feature,User Onboarding & KYC,Separate check during shareholder addition to check whether person filling is or is a beneficial owner. If they say yes then add his/ her details as a new shareholder (don't send shufti KYC verification link to that user) ,Low,Fixed,Onboarding --> Shareholder,Ishaan,Nishant,04-May-25,Dev,12-May-25,,8,
It1-223,Feature,Admin,"Summary/ Dashboard view - Should reflect exact fields on aggregate basis as per the tab - eg KYC Approval, Credit Assessement, .... Report ",Medium,In Progress,,Ishaan,Nishant,02-May-25,Dev,,,47,
It1-224,Feature,Lender Portal,"Option to deactivate risky/non-performing accounts - this will be done through Credit Line Activation/ Deactivtion button in ""Credit Line Activation"" Screen",Medium,Fixed,,Ishaan,Nishant,02-May-25,Dev,12-May-25,,10,
It1-224,Defect,User Onboarding & KYC,Despite missing mandatory infromation it is allowing me to submit the form - shouldn't be allowed. Also in Buyer in Review section everything is mandatory but while filling it is not. ,High,Fixed,MSME Onboarding screen,Aryan,Nishant,06-May-25,UAT,14-May-25,,8,
It1-225,Defect,Notifications,Credit Line Activated notification being shown only after KYC form is submitted,Low,Fixed,MSME Dashboard --> Notificatons,Sanyam,Nishant,06-May-25,UAT,16-May-25,,10,
It1-226,Defect,Admin,Ensure rule that Madad Admin can't approve KYC till Video KYC and Shareholder KYC is completed through Shufti,High,Fixed,Madad Admin --> KYC Approval,Ishaan,Nishant,06-May-25,UAT,12-May-25,A Pop on this message will come before Admin can change to Approved. ,6,
It1-227,Defect,Lender Portal,"Hovering on Credit Approval Action should show ""More Information Required""",Low,Fixed,Lender Portal --> Credit Applications,Ishaan,Nishant,06-May-25,UAT,8-May-25,,2,
It1-228,Feature,Lender Portal,Add option for service fee of fixed and % just like processing fee and add explanaton. Typically it will be fixed per drawdown. ,Low,Fixed,Lender Portal --> Credit Applications --> Offer,Ishaan,Nishant,06-May-25,UAT,12-May-25,,6,
It1-229,Defect,Contracts,"Credit Line Contract Signing Screen - KFS info repeated twice. Just keep one at the top of page. Offer Summary --> Change to Credit Line Key Fact Summary with Credit Limit, Tenure, Service Fee, Processing Fee. Key Terms & Conditions and Generation Information should all be as bullet points. ",Low,Fixed,Lender Portal --> Credit Applications --> Offer,Ishaan,Nishant,06-May-25,UAT,12-May-25,,6,
It1-230,Defect,Contracts,Credit Line and Invoice Term Loan contract has some defects - Interest Rate mentioned in Schedule 1 in cerdit line and schedule of EMI in invoice contract need to be removed. Facility fee and SErvice fee is wrongly been taken as 1% whereas value is 0.25%. ,Medium,Fixed,MSME Credit Line Offer Accept --> Contract Signing --> PDF of the contract,Aryan,Nishant,06-May-25,UAT,22-May-25,,43,
It1-231,Defect,User Dashboard,Credit Line details within Activation tab has commitment fee and late payment fee which was never shared before - should be removed,Medium,Fixed,MSME Portal --> Credit Line Activations --> Credit Line Overview,Ishaan,Nishant,06-May-25,UAT,9-May-25,,3,
It1-232,Defect,Admin,Credit Offer status is showing as Unknown,Medium,Fixed,Admin Portal --> Credit Assessment Tab --> Credit Offers,Ishaan,Nishant,06-May-25,UAT,12-May-25,,6,
It1-233,Feature,Lender Portal,Add Signage for action for Lender (eg Credit Applicaitons (if there are applications pending show within parenthesis or for Credit Line Activations any pending activations should be shown,Low,New,Lender Portal --> Credit Applications/ ACtivations,Aryan,Nishant,06-May-25,UAT,,,43,
It1-234,Feature,User Dashboard,My Invoices should have credit line available and credit line used at the top,Low,Fixed,MSME Portal --> My Invoices,Ishaan,Nishant,06-May-25,UAT,12-May-25,,6,
It1-235,Defect,Invoice,Invoices can be both PDF and Image. Though image can't be seen in preview,Low,Fixed,MSME Portal --> My Invoices,Ishaan,Nishant,06-May-25,UAT,9-May-25,Currently it will be only PDF,3,
It1-236,Defect,Lender Portal,"Download pending disbursals is also taking loan status of ""Lender Accepted"". It should not. ",Medium,Fixed,Lender Portal --> Payments --> Pending Disbursals,Ishaan,Nishant,06-May-25,UAT,12-May-25,,6,
It1-237,Defect,Lender Portal,Update EMI Table should get rid of Interest Column/ replace with service fee,Medium,Fixed,Lender Portal --> Payments --> Repayment Tracking,Ishaan,Nishant,06-May-25,UAT,12-May-25,,6,
It1-238,Defect,User Onboarding & KYC,Format of Privacy Policy and T&C need to be corrected,Low,Fixed,"MSME Consent --> Privacy, T&C",Aryan,Nishant,07-May-25,Dev,12-May-25,,5,
It1-239,Feature,User Onboarding & KYC,Make a rule/ config to refresh KYC to yearly cycle from last KYC approved status,Medium,Fixed,MSME Onboarding --> KYC,Ishaan,Nishant,07-May-25,Dev,13-May-25,,6,
It1-240,Feature,User Onboarding & KYC,"Check if the uploaded document expiry date is within allowance of +/- 3 months -> admin can also see it. In each doc where we are able to extract doc expiry date we show as small text above Approve/ Reject doc button as ""Document Expired/ About to Expire on <date of expiry>""",Medium,Fixed,Common,Aryan,Nishant,08-May-25,Dev,2-Jun-25,,41,
It1-241,Feature,Credit Risk,"Train CAM file on multiple bank account statement, cash flow, audited financial repot",Medium,Fixed,Madad Admin/ Lender Portal -->Credit Offer,Aryan,Nishant,08-May-25,Dev,20-May-25,,12,
It1-242,Feature,User Onboarding & KYC,Implement the document check also during shufti pro video KYC liveliness check,High,Fixed,,Ishaan,Abhishek,04-May-25,UAT,7-May-25,"KYB, AML screening do not come under the journey",3,
It1-242,Feature,Invoice,Invoice validation - duplicate invoice on Madad platform -> Add other criteria for duplication - to be provided,Medium,Duplicate,,Nishant,Abhishek,01-May-25,Dev,,For later iteration 3 ,48,
It1-243,Feature,User Dashboard,Define and agree customer complaint log and resolution report. A customer support contact need to be added to left hand panel which is always on for MSME to report. Also option to revoke consent should be there. ,Medium,Fixed,,Ishaan,Nishant,12-May-25,UAT,20-May-25,,8,
It1-243,Feature,User Onboarding & KYC,Add business address extracting from Bank Statement. Display that field in the Financials Tab after bank account and IBAN with an option to edit. ,Medium,Fixed,User Onboarding --> Financial + review,Ishaan,Nishant,09-May-25,Dev,12-May-25,,3,
It1-244,Feature,User Onboarding & KYC,Fraud reject reason in the activate/ deactivate for MSMEs should be included,Medium,Fixed,,Ishaan,Nishant,12-May-25,UAT,13-May-25,,1,
It1-244,Feature,Invoice,Add a button on Invoice upload to request changes to Invoice for User and Buyer (status changes Need more info). The request will go to Madad Admin to make changes (status changes to Pending Verification) ,Medium,Fixed,User Onboarding --> Invoice Upload,Ishaan,Nishant,09-May-25,Dev,12-Jun-25,Awaiting Prod Release,40,
It1-245,Feature,Dashboard & Report,Suspicious activity report through Shufti,Low,In Progress,,Nishant,Nishant,12-May-25,UAT,,Clarification from Shufti,37,
It1-246,Feature,Dashboard & Report,High risk transaction review report through Shufti,Low,In Progress,,Nishant,Nishant,12-May-25,UAT,,Clarification from Shufti,37,
It1-247,Feature,Lender and Admin Portal,Invoice loan status and repayment details against each invoice should be updated in all the portals - MSME/Madad/Lender,Medium,Fixed,,Ishaan,Abhishek,13-May-25,UAT,20-May-25,,7,
It1-248,Feature,User Onboarding & KYC,"In Eligibility criteria request call back should result in an email (or Jira ticket in further iteration) to be <NAME_EMAIL> with details of MSMEs elgibility details, failed status and phone",Medium,Fixed,User onboarding --> Eligibility --> Not eligible,Ishaan,Nishant,14-May-25,UAT,20-May-25,,6,
It1-249,Feature,User Onboarding & KYC,"In other documents upload, no restriction to upload documents",Medium,Fixed,User onboarding --> Business Tab --> More documents,Ishaan,Nishant,14-May-25,UAT,20-May-25,,6,
It1-250,Defect,User Onboarding & KYC,Add a rule to atleast add one buyer from MSME before allowing to submit onboarding form,Low,Fixed,User onboarding --> Review/ Submit,Ishaan,Nishant,14-May-25,UAT,20-May-25,,6,
It1-251,Defect,User Onboarding & KYC,In shareholder if the beneficial owner is self then pre-fill first shareholder as that person and it is locked.Other shareholder will then be added as needed. ,Low,Fixed,User onboarding --> Shareholder.,Ishaan,Nishant,14-May-25,UAT,22-May-25,,35,
It1-252,Defect,User Onboarding & KYC,"In review page remove QID number, Expiry, Nationality as mandatory field and add Contact number as mandatory field (take that from registeration)",Low,Fixed,User onboarding --> Review/ Submit,Aryan,Nishant,14-May-25,UAT,22-May-25,removed from personal section,35,
It1-253,Defect,User Onboarding & KYC + Admin,Shareholder mandatory fields are not reflecting as mandatory in review tab,Low,Fixed,Shareholder/ Review page,Aryan,Nishant,14-May-25,UAT,22-May-25,,35,
It1-254,Feature,Common,All current emails on eligibiity/ contact customer support etc should <NAME_EMAIL> with annotabtiion of Madad <followed by relevant subject>,Medium,Fixed,,Ishaan,Nishant,14-May-25,UAT,18-May-25,,4,
It1-255,Defect,Common,Sorting for applications/ credit offers/ othter always make it latest first + (pending action),Low,Fixed,,Aryan,Nishant,14-May-25,UAT,30-May-25,Sorting by status and date,35,
It1-256,Defect,User Onboarding & KYC,When changing the email midway of the journey can't be done,Low,Fixed,User registration page,Ishaan,Aryan,14-May-25,Prod,1-Jun-25,,35,
It1-257,Defect,User Onboarding & KYC,Financial data and credit beaure consent is not opening during eligibility process,Low,Fixed,Eligibility proceed screen,Aryan,Abhishek,14-May-25,Prod,20-May-25,,6,
It1-258,Feature,User Onboarding & KYC,"When uploading files , we're not able to upload more than 20mb but in real life it will be problamatic. Might need zipping upfront or reducing size. As an interim measure jsut add a message to the users for all docs in parenthesis saying ""ensure fomat x, y, z of size not more than 20MB""",Low,New,,Aryan,Abhishek,14-May-25,Prod,,Doc size allowed for only upto 20MB. ,35,
It1-259,Defect,User Onboarding & KYC,Account number and iban and address not getting extracted,Medium,Fixed,Financial section --> Bank statement extract,Aryan,Nishant,14-May-25,Prod,20-May-25,,6,
It1-260,Defect,User Onboarding & KYC,"Kyc primary applicant - avoid duplication of kyc of the primary , its happening during the journey and an email link is also sent, its hsould recognize , and no duplicate kyc",Medium,Fixed,User onboarding --> Shareholder addition,Ishaan,Abhishek,14-May-25,Prod,17-May-25,Redunduant as UBO and Applicant will be sent eKYC,3,
It1-261,Defect,User Onboarding & KYC,"In final reveiw page, the proceed button was gray when all the fields are even filled. It does allow to proceed though. ",Medium,Fixed,,Aryan,Abhishek,14-May-25,Prod,15-May-25,,1,
It1-262,Feature,Admin,overall verification status shouldn't be rejected,Low,Fixed,,Ishaan,Abhishek,14-May-25,Prod,18-May-25,,4,
It1-263,Feature,Admin,"In Onboarding journey , there are document statuses, keep clear error messsages and if uploading multiple docs then keep naming like that,, keep further detailing on that",Low,Fixed,,Ishaan,Abhishek,14-May-25,Prod,18-May-25,,4,
It1-264,Feature,Admin,"In Onboarding journey, add MSME kyb status as the first item",Medium,Fixed,KYC approval page for MSME,Ishaan,Nishant,14-May-25,Prod,15-May-25,,1,
It1-265,Feature,User Dashboard,"Document on onboarding keept in on profile and dropdown on dashboard, so overview beceome dashboard,, after onboarding, once profile is active, then we dont need onboarding section",,Fixed,,Ishaan,Abhishek,14-May-25,Prod,1-Jun-25,,35,
It1-266,Defect,Admin,Commercial registration in admin portal status is not showing correctly by shufti -> show proper status if shufty verificatin is pending,Low,Fixed,KYC approval page for MSME,Ishaan,Abhishek,14-May-25,Prod,20-May-25,,6,
It1-267,Defect,User Dashboard,Adress verification status (pending) should be removed (in activation journey),Medium,Fixed,User onboarding --> Review page ,Ishaan,Nishant,14-May-25,Prod,20-May-25,,6,
It1-268,Feature,User Onboarding & KYC,Personal/ main applicant: Address verification through enhanced Shufti verification need tobe implemented. This is based on geo tagging and first need to be clarified with Shufti.,Medium,Fixed,User onboarding --> Address in review/ summary,Ishaan,Abhishek,14-May-25,Prod,8-Jun-25,"Currently rule for country matching of shufti returned geo location/ country against Qatar. If not, highlighted in admin module for main applicant. ",35,
It1-269,Defect,User Onboarding & KYC,Applicant video kyc status is not showing even after doing video kyc in admin portal. This is probably an issue with two KYC being started in case the applicant is also the beneficial owner/ shareholder. Remove KYC trigger for self as it will be done later,Critical,Fixed,,Ishaan,Abhishek,14-May-25,Prod,17-May-25,Video KYC of applicant removed from main tabs as it will be done as part of shareholder reachout if they are an UBO.,3,
It1-270,Feature,User Onboarding & KYC,kyc request to shufti should be changed to ongoing KYC. In the Shufti KYC API payload 'ongoing kyc' should be enabled. ,Low,Fixed,KYC Shufti API parameters,Ishaan,Abhishek,14-May-25,Prod,2-Jun-25,This can only be done in live and not in trial,35,
It1-271,Feature,Admin,shareholder kyc logic for overall kyc need to be drived from individual verification status and document verfication. Once all the six fields are verified then overall changes to approved. ,Medium,Fixed,,Ishaan,Nishant,14-May-25,Prod,24-May-25,,35,
It1-272,Feature,User Onboarding & KYC,"Business Address verificaiton logic need to defined and implemented through Shufti. This is being extracted from bank address and to be matched against CR. If not matched, then highlight in admin for manual check. ",Medium,New,,Ishaan,Abhishek,14-May-25,Prod,,,35,
It1-273,Feature,Common,"add watermark of ""restricted"" on dowlnoaded documents",Low,Fixed,Download KYC documents or other documents,Aryan,Nishant,14-May-25,Prod,22-May-25,,35,
It1-274,Defect,Lender Portal,Error on activating credit line offer is resulting in error : error on validating processing fee --- validation path. The same error also appearing after signing the contract. ,Critical,Fixed,Credit Applicaitons --> Credit Offer and Activating credit line,Ishaan,Abhishek,14-May-25,Prod,15-May-25,,1,
It1-275,Defect,Invoice,Once a new buyer is added in case it is not part of top buyers then doesn't return to last uploaded screen and invoice has to be uploaded again,Medium,Fixed,User Dashboard --> Upload Invoice --> New Buyer,Ishaan,Nishant,15-May-25,Prod,20-May-25,,5,
It1-276,Feature,Buyer Dashboard,New Buyer login - unique link to be sent to buyer at the time of invoice verification. Force new buyer to login and register in the portal as part of invoice verification,High,Fixed,Invoice verification,Ishaan,Nishant,15-May-25,Prod,20-May-25,,5,
It1-277,Defect,User Onboarding & KYC,Eligibility check the financial data and credit bureau consent link is not working,Low,Duplicate,,Aryan,Abhishek,18/05/2025,Prod,,,31,
It1-278,Defect,Invoice,The buyer and the invoice check implementation is flawed.,Medium,Fixed,,Ishaan,Abhishek,18-May-25,Prod,20-May-25,,2,
It1-279,Defect,Buyer Dashboard,"The buyer list is not reflected, neither is the business able to add any buyer to submit new invoices against the buyer",Medium,Fixed,,Ishaan,Abhishek,18-May-25,Prod,20-May-25,,2,
It1-280,Feature,Regulator Dashboard,"Add a new user ""Compliance Officer/ CO"" for all ongoing compliance reporting",High,Fixed,,Ishaan,Nishant,21-May-25,UAT,5-Jun-25,,28,
It1-281,Feature,Admin + Regulator Dashboard,Customer Onboarding Report - Tech : Unit and Aggregate. Will be shown through export as table functionality in various views.  Add PEP column,Medium,Fixed,,Ishaan,Abhishek,21-May-25,UAT,16-Jun-25,This will be implemented via Export Button of the table on MSME Credit Offer Table,28,
It1-282,Feature,Admin + Regulator Dashboard,Customer Complaint Log ,Medium,In Progress,,Ishaan,Abhishek,21-May-25,UAT,,,28,
It1-283,Feature,Regulator Dashboard,Software Defects Log - Tech,Medium,In Progress,,Ishaan,Abhishek,21-May-25,UAT,,,28,
It1-284,Feature,Regulator Dashboard,QA Testing Report - Tech,Medium,New,,Ishaan,Abhishek,21-May-25,UAT,,,28,
It1-285,Feature,Regulator Dashboard,Security Threat Report - High Priority Logs from Firewall/ Security monitoring,Medium,In Progress,,Abdulrehman,Abhishek,21-May-25,UAT,,,28,
It1-286,Feature,Admin + Regulator Dashboard,AML High Risk/PEP Customer Report - Tech,Medium,In Progress,,Ishaan,Abhishek,21-May-25,UAT,,,28,
It1-287,Feature,Admin + Lender + Regulatory Dashboard,"Ability to export the table as excel on various views - eg KYC View/ Credit Offer View/ Credit Application View. This facility should be provided to Admin, Lender ",High,Fixed,,Ishaan,Nishant,22-May-25,Dev,22-May-25,"This is enabled for MSME, Admin and Lenders",27,
It1-288,Defect,Invoice,OTP from post invoice contract/ KFS should be removed,Low,Fixed,,Ishaan,Abhishek,25-May-25,Prod,26-May-25,,24,
It1-289,Defect,Common,UAT link is sent for email verification in production,Medium,Fixed,,Ishaan,Abhishek,26-May-25,Prod,26-May-25,,23,
It1-290,Defect,User Onboarding & KYC,User is unable to edit the email once entered when registering for the first time via phone no.,Medium,Fixed,,Ishaan,Abhishek,26-May-25,Prod,1-Jun-25,,23,
It1-291,Defect,User Onboarding & KYC,"When user select yes in the shareholder section the option to enter name and last name is blocked, but in the review section these details are highlighted as empty",High,Fixed,,Ishaan,Abhishek,26-May-25,Prod,27-May-25,,23,
It1-292,Defect,User Onboarding & KYC,while financial document upload the audit statement is not recognised and error mentions AI,Low,Fixed,User Onboarding --> Financial Section,Aryan,Abhishek,26-May-25,Prod,26-May-25,,23,
It1-293,Defect,Admin,In the madad admin panel while reviewing the document it is showing 10 additional document whereas the documents are not even uploaded,Low,Fixed,,Ishaan,Abhishek,26-May-25,Prod,16-Jun-25,,23,
It1-294,Defect,Invoice,"Invoice is read incorrectly, the buyer and seller (customer/ MSME) details are swapped",High,Fixed,,Ishaan,Abhishek,26-May-25,Prod,1-Jun-25,,23,
It1-295,Feature,Infrastructure,System and App Log retention by default should be for 3 months,Medium,New,,Abdulrehman,Nishant,26-May-25,Prod,,,23,
It1-296,Feature,User Dashboard & Buyer Dashboard,Buyer and MSME Dashboard is being merged as one as per new design,High,Fixed,,Ishaan,Nishant,24-May-25,UAT,1-Jun-25,,25,
It1-297,Feature,User Dashboard,MSME Design for GoLive,High,Fixed,,Ishaan,Abhishek,22-May-25,UAT,1-Jun-25,,27,
It1-298,Feature,Buyer Dashboard,Buyer Design for GoLive,High,Fixed,,Ishaan,Abhishek,22-May-25,UAT,1-Jun-25,,27,
It1-299,Feature,Lender Portal,Lender Design for GoLive,High,In Progress,,Abhishek,Nishant,27-May-25,UAT,,,22,
It1-300,Feature,Admin,Admin Design for GoLive,High,New,,Abhishek,Nishant,27-May-25,UAT,,,22,
It1-301,Feature,User Dashboard,"Check if CR name and Bank Statement Name and Address is matching
",Medium,In Progress,,Ishaan,Abhishek,28-May-25,UAT,,,,
It1-302,Defect,User Onboarding & KYC,Phone number should be verified for email and gmail based registeration. This would enable MFA for all the scenarious.,Low,Fixed,MSME registration,Ishaan,Abhishek,29-May-25,Prod,1-Jun-25,,,
It1-303,Defect,User Onboarding & KYC,Get OTP button on email is not sending OTP messages,Low,Fixed,User registration --> email OTP Verification,Ishaan,Nishant,01-June-25,UAT,1-Jun-25,,,
It1-304,Defect,Invoice,"Buyer addition flow is missing in new design from the invoice section, if the invoice added for a new buyer",Medium,Fixed,,Ishaan,Abhishek,02-June-25,,14-Jun-25,,,
It1-305,Feature,Infrastructure,Add a new read only user in Prod DB called DB-Auditor1. This will be used for policy audit checks. The password will change for this user every 3 months or upon closure of an audit. ,Medium,Fixed,,Abdulrehman,Nishant,02-June-25,Prod,13-Jun-25,,,
It1-306,Feature,Infrastructure,Add a new read only user in GCP called GCP-Auditor1 with access to Prod subscriptions. This will be used for policy audit checks. The password will change for this user every 3 months or upon closure of an audit. ,Medium,In Progress,,Abdulrehman,Nishant,02-June-25,Prod,,,,
It1-307,Defect,User Onboarding & KYC,Shufti implementation is incorrect - check ref id :commercialRegistration_KYBAML_63f79676e881.,High,Fixed,,Ishaan,Abhishek,04-June-25,Prod,14-Jun-25,,,
It1-308,Feature,Invoice,Invoice submitted by an MSME will come to Madad admin first for approvbal before going to Buyer's queue as per updated flow,High,New,,Ishaan,Abhishek,04-June-25,Prod,,,,
It1-309,Feature,Credit Risk,Credit Report - Shareholder % in the table,Low,In Progress,,Aryan,Nishant,04-June-25,Prod,,,,
It1-310,Feature,Credit Risk,Credit Report - Expiry date should be highlighted in KYC page,Medium,Fixed,,Aryan,Nishant,04-June-25,Prod,6-Jun-25,,,
It1-311,Feature,Credit Risk,Credit Report - Cash Positions,Low,In Progress,,Aryan,Nishant,04-June-25,Prod,,,,
It1-312,Defect,User Onboarding & KYC + Lender,Remove QIB and Bank Name in Production. Let it remain in UAT,Medium,Fixed,MSME Onboarding --> ,Ishaan,Ravindra,05-June-25,Prod,10-Jun-25,,,
It1-313,Feature,User Onboarding & KYC,Trade number extraction from trade licese number not happening - it was null,Low,New,,Aryan,Nishant,05-June-25,Prod,,,,
It1-314,Feature,User Onboarding & KYC,Extraction of UBOs from CR doc ,Low,New,,Aryan,Ravindra,05-June-25,Prod,,,,
It1-315,Feature,Credit Risk,"Model CAM file with extraction/ metrics for 
 
- Key extract of CCR (Credit Report)
- Key Extract Summary of Audited financial report

This report is generated after KYC of MSME is approved taking respective fields from collections and is made available for FI and Admin. ",High,New,Madad Admin/ Lender Portal -->Credit Offer,Abhishek,Nishant,31-May-25,Dev,,More sample docs needed,18,
It1-316,Defect,User Onboarding & KYC,Nationality extraction from QID or Passport upload,Low,New,,Aryan,Abhishek,05-June-25,Prod,,,,
It1-317,Defect,Admin,CR doc check is showing as Submitted - it should be Pending as Shufti API call was done,Medium,Fixed,Madad Admin --> MSME KYC Verification,Ishaan,Abhishek,05-June-25,Prod,13-Jun-25,,,
It1-318,Defect,Lender Portal,Remove Shufti reference number (CR) and Shufti not done kind of reference ,Low,Fixed,Lender --> MSME KYC,Ishaan,Abhishek,05-June-25,Prod,16-Jun-25,To be pushed in Prod as of 16 June,,
It1-319,Defect,Contracts,Remove Interest (change it to Sevice fee) reference from credit line contract,Low,In Progress,MSME Credit Offer --> Contract,Aryan,Abhishek,05-June-25,Prod,,,,
It1-320,Defect,User Dashboard,Pop up to confirm credit line activated ,Low,Fixed,MSME Credit Offer --> Accepted,Ishaan,Abhishek,05-June-25,Prod,12-Jun-25,,,
It1-321,Feature,Invoice,Remove Buyer Address from each Invoice extracted,Low,Fixed,Invoice upload,Ishaan,Abhishek,05-June-25,Prod,15-Jun-25,,,
It1-322,Feature,Invoice,"Add additional doc in Invoice supporting as + sign rather than doc 1, 2, 3, 4",Low,Fixed,Invoice Upload --> Supporting Doc,Ishaan,Abhishek,05-June-25,Prod,16-Jun-25,To be pushed in Prod as of 16 June,,
It1-323,Feature,Buyer Dashboard,Invoice Total and Disbursed (replicate from lender screen),Low,Fixed,,Ishaan,Abhishek,05-June-25,Prod,16-Jun-25,To be pushed in Prod as of 16 June,,
It1-324,Defect,Lender Portal,Lender should have option to put - MORE INFORMATION REQUIRED status for Invoices - as they do KYC confirmation and then credit assessment,Low,New,,Ishaan,Abhishek,05-June-25,Prod,,,,
It1-325,Feature,Invoice,"For each invoice discounted we should show the status of discounted contract: 
- EMIs paid, Amoung paid/ pending, status of invoice, 
Link each discounted invoice with a link to repayment status for admin, lender, msme",Low,In Progress,MSME Dashboard --> Invoice (after EMI repayments),Ishaan,Abhishek,05-June-25,Prod,,,,
It1-326,Feature,Admin ,Add deactivated categories - 'Delinquent (customer behind payment schedule); Fraud (potential fraud investigated or determine); Customer Requested (mostly as a result of consent withdrawal),Medium,Fixed,,Ishaan,Nishant,06-June-25,UAT,16-Jun-25,To be pushed in Prod as of 16 June,,
It1-327,Feature,Onboarding,"If PEP is identified then ask source of wealth, Ask source of wealth for all the shareholders during shareholder KYC screen",Low,New,,Ishaan,Abhishek,07-June-25,UAT,,,,
It1-328,Feature,Regulator Dashboard,Data Breach Incident Report,High,In progress,Login as Compliance Officer ,Abdulrehman,Nishant,07-June-25,UAT,,,,
It1-329,Debug,Common,"Change Consolidated Credit Report to ""Commercial Credit Report"" This should be changes in all parts of code in frontend (field consolidated credit report) and backend (eg document type)",Medium,In progress,,Aryan,Nishant,10-June-25,UAT,,,,
It1-330,Feature,User Onboarding & KYC,Add W.L.L to the list of Company type in the eligibility check,Low,New,Eligibility criteria,Aryan,Abhishek,11-June-25,Prod,,,,
It1-331,Feature,Common,Update privacy policy content - https://docs.google.com/document/d/1qjHZUWIri6C13678rT0jXqOECo8VUv6I9nBbpUhTUoE/edit?usp=sharing ,Medium,In progress,,Aryan,Abhishek,11-June-25,Prod,,,,
It1-332,Feature,Common,Update the terms and condition content - https://docs.google.com/document/d/1qjHZUWIri6C13678rT0jXqOECo8VUv6I9nBbpUhTUoE/edit?usp=sharing,Medium,In progress,,Aryan,Abhishek,11-June-25,Prod,,,,
It1-333,Feature,User Onboarding & KYC,Change the 6 month CR validity criteria to 2 months; Make non eligible only if CR validity is less than 2 months,Low,New,Onboarding ---> Business CR Upload,Aryan,Abhishek,11-June-25,Prod,,,,
It1-334,Feature,User Onboarding & KYC,"Based on feedback of couple of onboardings, only four mandatory doc to be there - CR, Trade License, Establishment, Tax. Rest mandatory status should be removed 
Rules (eg atleast one buyer and shareholder) on mandatory checks to be removed  ",High,Fixed,,Ishaan,Ravindra,12-June-25,UAT,16-Jun-25,,,
It1-335,Feature,User Onboarding & KYC,"Based on feedback of couple of onboardings, no triggering of KYC link automatically to shareholders. Rather an option to MSME should come whether they want to send the link. But in most conditions Madad Admin from backend will trigger after talking to MSMEs for initial set of cases",High,New,,Ishaan,Ravindra,12-June-25,UAT,,,,
It1-336,Feature,Infrastructure,Add HTTP deny rule in firewall,Medium,New,,Sanyam,Nishant,16-June-25,Prod,,,,
It1-337,Feature,Common,Create separate micro-service for Data Masking at Code level and then take a mask based view for DB level. ,High,In progress,,Ishaan,Nishant,16-June-25,UAT,,,,