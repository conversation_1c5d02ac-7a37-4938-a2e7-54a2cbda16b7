Bucket,Requirement,Details,Tech,v1,v2,v3,Comments,Owner,Status
DESIGN,,,,1,1,1,,,
,Update platform screens to new design,,,,1,,,,
,,,,,,,,,
WEBSITE,,,,1,1,1,,,
,Home page,,,,1,,,,
,"How can you discount invoice, benefits, ",,,,1,,,,
,Check eligibility of your business for ID on Madad,,,,,1,,,
,What is Invoice Discounting,,,,1,,,,
,How can you discount your invoice in Madad,,,,1,,,,
,About Us,,,,1,,,,
,"Madad is Shariah Compliant, how , what, fatwa etc.",,,,1,,,,
,"Page for FIs - What, How, When etc.",,,,,1,,,
,"Contact us - Email, phone number, form",,,,1,,,,
,Policies - Customer specific policies,,,,1,,,,
,,,,,,,,,
DISTRIBUTION,,,,1,1,1,,Aryan,
,In browser,,"Chrome, Safari, Opera, Edge",1,,,,,
,"Mobile responsive, in browser",,"Chrome, Safari, Opera, Apple phones, Android phones",,1,,,,Pending
,Desktop App,,"iOS, Android",,,1,,,
,Mobile App,,"iOS, Android",,,1,,,
,,,,,,,,,
OVERALL ACCOUNT FUNCTIONALITIES,,,,1,1,1,,Aryan,
,"Role based access to MSMEs, Madad and FIs",,,,,1,,,
,Support feature to raise concerns / queries,,,,1,,,,Pending
,Profile Section,,,1,1,,,,Pending
,,,,,,,,,
SIGNUP/LOGIN - ALL STAKEHOLDERS,,,,1,1,1,,Ishaan,
,"One signup/login flow for all stakeholders i.e. MSME, FIs, Admin etc.",,,,1,,Nishant to check and update,,Pending
,MSME: Phone + OTP flow,,QatarSMS.net,,1,,,,Completed
,MSME: Email flow,,,1,,,,,
,MSME: Google email Auth flow,,Gmail,1,,,,,
,"FI: login screen (with login id, pass)",,,1,,,,,
,"Buyer: login screen (with login id, pass)",,,1,,,,,
,"Madad Admin: login screen (with login id, pass)",,,1,,,,,
,,,,,,,,,
ONBOARDING (Application Submission and Review),,,,1,1,1,,Ishaan,
MSME Portal,,,,,,,,,
,Ability to upload Documents,,,1,,,,,
,Add more documents upload if needed (no ristrictions on number of documents),,,,1,,,,Pending
,Check if the uploaded document is the intended document,,OCR,,1,,,Aryan,Pending
,Check the orginality of the document,,Shufti,,1,,"Shufti validates - tax , cr, trade license",,Complete
,Check documents' authenticity with the issuing authority,,MOCI integration,,,1,,,
,Check red flags in document e.g. expiry,,OCR,,1,,,,Pending
,Read all information in the documents and collect in database (auto),,OCR,1,1,,OCR is pending on some financial documents like cash flows,,Complete
,Editable review screen for application submission (with pre-filled values),,,1,,,,,
,Auto-fill information based on submissions at each step,,,1,1,,,,Complete
,e-KYC links in all relevant channels (with liveliness checks),,Shufti,,1,,,,Pending
,Auto-reject applications in case of red flags,,BRE,,,1,Abhishek to update based on credit assessment SOP from Rashid,,
,Functionality to self e-mail the submitted application with the documents,,,,1,,,,Pending
,Manually upload Buyers list,,,1,,,,,
,,,,,,,,,
Madad Portal,,,,,,,,,
,MSME wise view on the completion of the application,,,,1,,,,Complete
,Activity log at MSME level to capture any activity performed by/for the MSME,,,,1,,"View to be finalised, Nishant to confirm",,Pending
,Functionality to process and take decision on application,,,1,,,,,
,Manual kyc verification,,Manual KYC SOP,,1,,,,
,Auto document verification status,,OCR + Shufti,,1,,,,
,Re-ignite dropped / in-process applications through admin portal,,,,,1,,,
,Provision to upload any missing/incorrect document to the application,,,,1,,"Problem: If MSMEs interest level is low, they delay uploading missing documents (hypothesis)",,
,"Unique ID for the MSME , which maps to a unique sanction id supplied by the lender",,,,1,,,,Complete - Unique ID for MSME
,MSME Unique ID mapping to a unique sanction id supplied by the lender,,,,,1,,,
,,,,,,,,,
CREDIT ASSESSMENT,,,,1,1,1,,Ishaan,
,Authenticity of the business,,MOCI integration,,,1,,,
,Credit history of business through Bureau integration,,Credit Bureau,,,1,,,
,Manual buyer verfication for existence of the company and relationship with MSME,,Buyer Verification SOP,1,1,,Offline verification of buyers doesn't have any mechanism to enter on the platform,,
,Buyer verification via email,,,,,1,Need to further elaborate the method,,
,"Auto whitelist buyers based on FI lists, other MSME buyer whitelisting etc.",,,,,1,,,
,Auto Bank statement analysis,,OCR,,1,,,,Pending
,,PDF + Excel Format,,,,,,,
,,Image captured or Scanned format,,,,,,,
,,QNB bank statement,,,,,,,
,,other banks,,,,,,,
,Manual Credit Assessment with Fraud detection etc.,,Credit Assess. SOP,,1,,,,
,Auto credit assessement,,,,,1,,,
,Auto fraud detection,,Credit Assess. SOP,,1,,,,Pending
,Manual highlight AML/PEP accounts,,Credit Assess. SOP,,1,,,,
,Auto highlight AML/PEP accounts,,Shufti,,1,,,,Complete
,"Madad Score (along with FI, Credit Bureau etc.)",,BRE,,,1,Check with Rashid on the scoring algo,,
,CAM file generation for the FIs,,,,1,1,,,Pending
,,,,,,,,,
"MARKETPLACE (Credit Offer, Contract Signing and Line Activation)",,,,1,1,1,,Aryan,
Madad Portal,,,,,,,,,
,Share MSME applications with FIs,,,1,,,,,
,CAM file Shared with FIs,,,,1,,,,Pending
,Credit offers received and accepted status at MSME level,,,,1,,,,Pending
,Provision to upload physically signed contract for any credit offer accepted,,Activation SOP,,1,,,,Pending
,Penny Drop check for bank account status,,Activation SOP ,,,1,,,
,Automated disbursal bank account verification check,,Bank Integration,,,1,wishlist for scale,,
,,,,,,,,,
FI Portal,,,,,,,,,
,FI download all submitted documents (Business and UBO),,,1,,,,,
,FI reject any un-acceptable document (Business and UBO),,,1,,,,,
,FI ask for more information or documents (Business and UBO),,,1,,,,,
,FI can approve/reject/hold an application (Business and UBO),,,1,,,,,
,FI can give credit line offfer for the MSME,,,1,,,,,
,,,,,,,,,
MSME Portal,,,,,,,,,
,MSME can view/accept the credit offer,,,1,,,,,
,MSME can sign the contract post accepting credit offer,,,1,,,,,
,,,,,,,,,
INVOICE DISCOUNTING,,,,1,1,1,,Ishaan,
MSME Portal,,,,,,,,,
,MSME upload invoice and other supporting trade documents,,,1,,,,,
,Auto check invoice against whitelisted buyers,,,,1,,,,Complete
,Invoice validation - duplicate invoice on Madad platform,,,,1,,,,Complete
,Invoice validation - tempered invoice checks etc.,,,,,1,,,
,Bulk Invoice upload feature - across buyer base,,,,,1,,,
,,,,,,,,,
Madad Portal,,,,,,,,,
,View/Edit access of invoice submitted for discounting,,,,1,,,,Complete
,Sharia compliant product structure,,,,1,,,,Pending
,Loan tape in the database to calculate and store all the fees against any invoice discounted,,MongoDB,,1,,,,Pending
,"Unique transaction ID for any invoice discounted , which maps to a unique loan id supplied by the lender",,,,,1,,,
,Status of contract signed by MSME for invoice discounting,,,,1,,,,Pending
,Status against each invoices,,,,1,,,,Pending
,Risk triggers based on transaction and repayment behaviour,,BRE,,,1,,,
,Provision to upload physically signed contract of invoice discounting,,,,,1,,,
,,,,,,,,,
Buyer Portal,,,,,,,,,
,Approve/Reject the invoices submitted by MSME,,,1,,,,,
,Request any change in invoice to Madad/MSME ,,,1,,,,,
,,,,,,,,,
FI Portal,,,,,,,,,
,FI download invoice and related documents,,,1,,,,,
,FI reject any un-acceptable invoice or related document ,,,1,,,,,
,FI ask for more information on specific invoice ,,,1,,,,,
,FI can approve/reject/hold an invoice discounting request,,,1,,,,,
,View contract signed by the MSME,,,1,,,,,
,,,,,,,,,
DISBURSAL & REPAYMENT*,,,,1,1,1,,Ishaan,
FI Portal,,,,,,,,,
,Manually download the details of pending disbursals,,,1,,,,,
,Manually update of disbursals made bt the FIs,,,1,,,,,
,Manual update of repayments received against any loan/invoice,,,1,,,,,
,Integration with bank to share disbursal request,,Chron Job,,,1,,,
,,,,,,,,,
Madad Portal,,,,,,,,,
,Reflect appropriate disbursal and repayment status,,,,1,,,,Pending
,,,,,,,,,
MSME Portal,,,,,,,,,
,Reflect appropriate disbursal and repayment status,,,,1,,,,Pending
,,,,,,,,,
DASHBOARDS,,,,1,1,1,,Aryan,
MSME Portal,,,,,,,,,
,"Summary view - Agregate on Whitelisted Buyer, Credit Line ",,Firebase/Looker,,1,,,,Pending
,,,,,,,,,
Madad Portal,,,,,,,,,
,"Summary view - Agregate on MSME, FI, Whitelisted Buyer and Invoices Dicounted",,Firebase/Looker,,1,,,,Pending
,,,,,,,,,
FI Portal,,,,,,,,,
,"Summary view - Agregate on MSME, and Whitelisted Buyer",,Firebase/Looker,,1,,,,Pending
,Option to deactivate risky/non-performing accounts ,,,,1,,,,Pending
,Option to modify the credit limit/offer,,,,,1,,,
,,,,,,,,,
RECONCILIATION,,,,1,1,1,,Ishaan,
,Payment gateway integration,,,,,1,,,
,Integration with FIs/Banks to sync the credit line level details dynamically,,REST APIs,,,1,,,
,"Invoice specific account / money reconcilliation view - MSME portal, Admin portal, FI Portal",,,,1,,,,Pending
,,,,,,,,,
SECURITY AND COMPLIANCE,,,,1,1,1,,Sanyam,
,Adhere all the compliances mentioned here: Check list - Regulatory and Compliant,,,,1,1,Nishant will review and update the next iteration contenders,,Pending
,,,,,,,,,
,,,,,,,,,
NOTIFICATION,,,,1,1,1,,Sanyam,
,"Communication at specific changes in application process (when, format, channel, frequency etc.)",,SMS/Email / Push Notification,1,1,,,,Pending
,Updated notification content,,SMS/Email / Push Notification,,1,,Nishant to update the steps,,Pending
,Notification center enhancements,,Push Notification,1,1,,,,Pending
,Reminder to MSME about Due date ,,SMS/Email / Push Notification,,1,,,,Pending
,"Option to resend communication (SMS, Email) to the Buyer for the invoice approval",,SMS/Email / Push Notification,,,1,,,
,Option to trigger repayment link against each pending invoice,,SMS/Email / Push Notification,,,1,,,
,Notification on successful disbursal from FIs/Banks against an invoice,,SMS/Email / Push Notification,,1,,,,Pending
SANDBOX REPORTING,,,,1,1,1,,,
,Customer Onboarding Report - Tech,,,,1,,,,
,,Unit level for MSME/Buyer/FI,,,1,,,,
,,Aggregate level for MSME/Buyer/FI,,,1,,,,
,,"FIlter level view to identify - Msme type, Stage, Acceptance, Sector,AML/PEP risk identified",,,1,,,,
,Customer Complaint Log - Offline,,,,1,,,,
,,Jira Ticket for customer complaint log,,,,1,,,
,,Click and send for the email provided in the platform,,,1,,,,
,Complaint Resolution Report - Offline,,,,1,,,,
,,Complain template to be provided by Fundfina,,,1,,,,
,Software Defects Log - Tech,,,,1,,,,
,,Any bug in madad platfor to be conerted into defects and reported,,,1,,,,
,,To be made available in the regulator portal,,,1,,,,
,QA Testing Report - Tech,,,,1,,,,
,,Regulator portal to be created,,,1,,,,
,,QA reports to be published to this portal,,,1,,,,
,Data Breach Incident Report - Offline,,,,1,,,,
,,Email is triggered to Madad internal teams and stakeholders to report any incident from the firewall log,,,1,,,,
,,All such emails are compiled to a single report triggered bi-weekly,,,1,,,,
,Security Threat Report - Offline,,,,1,,,,
,,Email is triggered to Madad internal teams and stakeholders to report any incident from the firewall log,,,1,,,,
,,All such emails are compiled to a single report triggered bi-weekly,,,1,,,,
,Fraud Investigation Report - Offline,,,,1,,,,
,,KYC related fraud detection & reporting,,,1,,,,
,,Buyer reported fraud reporting,,,1,,,,
,,Bank/FI reported fraud reporting,,,1,,,,
,,Madad admin can mark identified fraud,,,1,,,,
,,Account deactivation to have input capture fraud,,,1,,,,
,,Chron Job to send monthly report compiling all the reporta,,,1,,,,
,Fraud Incident Log - Offline,Platform to enable the monthly reporting as done for investigation reporting,,,1,,,,
,AML Suspicious Activity Report Log - Tech,,,,1,,,,
,AML High Risk/PEP Customer Report - Tech,,,,1,,,,
,AML High-risk Transaction Review Report - Tech,,,,1,,,,
,,Fundfina to enable the transaction review as a part of Shufti integration,,,1,,,,
,,Report is published monthly to highlight high-risk transactions ,,,1,,,,
,Risk Control System Document - Offline,,,,1,,,,