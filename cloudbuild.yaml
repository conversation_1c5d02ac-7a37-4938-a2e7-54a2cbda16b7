steps:
  - name: "gcr.io/cloud-builders/docker"
    args:
      [
        "build",
        "-t",
        "${_AR_HOSTNAME}/${_AR_PROJECT_ID}/${_AR_REPOSITORY}/${REPO_NAME}:${SHORT_SHA}",
        "--build-arg",
        "NEXT_PUBLIC_API_URL=${_NEXT_PUBLIC_API_URL}",
        ".",
      ]

  - name: "gcr.io/cloud-builders/docker"
    args:
      [
        "push",
        "${_AR_HOSTNAME}/${_AR_PROJECT_ID}/${_AR_REPOSITORY}/${REPO_NAME}:${SHORT_SHA}",
      ]

  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
    entrypoint: gcloud
    args:
      - "run"
      - "deploy"
      - "${_SERVICE_NAME}"
      - "--image"
      - "${_AR_HOSTNAME}/${_AR_PROJECT_ID}/${_AR_REPOSITORY}/${REPO_NAME}:${SHORT_SHA}"
      - "--region"
      - "${_DEPLOY_REGION}"
      - "--platform"
      - "${_PLATFORM}"
      - "--allow-unauthenticated"
timeout: 1200s
