{"name": "nextjs-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3003", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "autoprefixer": "^10.4.20", "axios": "^1.8.4", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "heroicons": "^2.2.0", "immer": "^10.1.1", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "next": "^15.2.4", "pdf-lib": "^1.17.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^5.2.0", "recharts": "^2.15.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5"}}